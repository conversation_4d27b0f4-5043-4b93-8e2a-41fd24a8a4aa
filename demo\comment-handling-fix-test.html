<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注释处理修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-case {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-case h3 {
            color: #555;
            margin-top: 0;
        }
        .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .before {
            border-left: 4px solid #f56c6c;
        }
        .after {
            border-left: 4px solid #67c23a;
        }
        .btn {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn:hover {
            background: #66b1ff;
        }
        .btn:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }
        .result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .result.pass {
            background: #e1f3d8;
            color: #67c23a;
            border: 1px solid #b3d8a4;
        }
        .result.fail {
            background: #fef0f0;
            color: #f56c6c;
            border: 1px solid #f5a9a9;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 2px;
        }
        .problem {
            background: #f8d7da;
            padding: 2px 4px;
            border-radius: 2px;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 注释处理修复测试</h1>
        <p>专门测试修复后的注释处理逻辑，防止重复格式化时注释吞噬后续代码</p>

        <div class="test-case">
            <h3>问题演示</h3>
            <p><strong>问题描述：</strong>重复格式化时，单行注释会吞噬同一行后面的有用代码</p>
            <div class="comparison">
                <div>
                    <h4>❌ 修复前的问题</h4>
                    <div class="code-block before">// 先按 value 排序  const valueCompare = a.TEMPLATE_CODE.localeCompare(b.TEMPLATE_CODE);
if (valueCompare !== 0) return valueCompare;
// 如果 value 相同，则按 key 排序  return a.EQUIPMENT_NAME - b.EQUIPMENT_NAME;</div>
                    <p>第二次格式化后：</p>
                    <div class="code-block before">// 先按 value 排序  const valueCompare = a.TEMPLATE_CODE.localeCompare(b.TEMPLATE_CODE); if(valueCompare !== 0)return valueCompare;</div>
                    <p class="problem">⚠️ 第4-5行的有用代码被注释吞噬了！</p>
                </div>
                <div>
                    <h4>✅ 修复后的效果</h4>
                    <div class="code-block after">// 先按 value 排序
const valueCompare = a.TEMPLATE_CODE.localeCompare(b.TEMPLATE_CODE);
if (valueCompare !== 0) return valueCompare;
// 如果 value 相同，则按 key 排序
return a.EQUIPMENT_NAME - b.EQUIPMENT_NAME;</div>
                    <p class="highlight">✅ 注释和代码正确分离，重复格式化安全</p>
                </div>
            </div>
        </div>

        <div class="test-case">
            <h3>测试用例</h3>
            <button id="runAllTests" class="btn">运行所有测试</button>
            <button id="clearResults" class="btn">清空结果</button>
            <div id="testResults"></div>
        </div>

        <div class="test-case">
            <h3>修复要点</h3>
            <h4>1. 问题根源</h4>
            <ul>
                <li>原始正则：<code>/\/\/.*$/gm</code> 会匹配从 <code>//</code> 到行尾的所有内容</li>
                <li>当代码被压缩成一行后，注释后的所有代码都被当作注释内容</li>
                <li>重复格式化时，有用代码被错误地保护为注释</li>
            </ul>
            
            <h4>2. 修复方案</h4>
            <ul>
                <li>使用逐字符解析替代正则表达式</li>
                <li>精确识别注释边界，避免贪婪匹配</li>
                <li>在遇到分号、换行或代码结构时停止注释匹配</li>
                <li>优先处理多行注释，然后处理单行注释</li>
            </ul>
            
            <h4>3. 安全边界</h4>
            <ul>
                <li>换行符：<code>\n</code> 或 <code>\r</code></li>
                <li>分号：<code>;</code></li>
                <li>代码开始：空格后跟字母、下划线或美元符号</li>
            </ul>
        </div>
    </div>

    <script>
        // 修复后的安全注释保护方法
        function safeCommentProtection(code, commentPlaceholders, startIndex) {
            let result = '';
            let i = 0;
            let commentIndex = startIndex;
            
            while (i < code.length) {
                // 检查多行注释
                if (i < code.length - 1 && code[i] === '/' && code[i + 1] === '*') {
                    let commentStart = i;
                    i += 2; // 跳过 /*
                    
                    // 查找注释结束
                    while (i < code.length - 1) {
                        if (code[i] === '*' && code[i + 1] === '/') {
                            i += 2; // 跳过 */
                            break;
                        }
                        i++;
                    }
                    
                    const comment = code.substring(commentStart, i);
                    const placeholder = `__COMMENT_${commentIndex++}__`;
                    commentPlaceholders.push({ placeholder, content: comment });
                    result += placeholder;
                    continue;
                }
                
                // 检查单行注释
                if (i < code.length - 1 && code[i] === '/' && code[i + 1] === '/') {
                    let commentStart = i;
                    i += 2; // 跳过 //
                    
                    // 查找注释结束 - 只到行尾或分号
                    while (i < code.length) {
                        const char = code[i];
                        // 遇到换行符、分号、或者可能的代码结构就停止
                        if (char === '\n' || char === '\r' || 
                            char === ';' || 
                            (char === ' ' && i + 1 < code.length && /[a-zA-Z_$]/.test(code[i + 1]))) {
                            break;
                        }
                        i++;
                    }
                    
                    const comment = code.substring(commentStart, i);
                    const placeholder = `__COMMENT_${commentIndex++}__`;
                    commentPlaceholders.push({ placeholder, content: comment });
                    result += placeholder;
                    continue;
                }
                
                // 普通字符
                result += code[i];
                i++;
            }
            
            return result;
        }

        // 修复后的格式化方法（简化版用于测试）
        function formatJavaScriptFixed(code) {
            if (!code || code.trim() === '') return code;
            
            try {
                // 1. 保护字符串
                const strings = [];
                let stringIndex = 0;
                let formatted = code.replace(/(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g, (match) => {
                    const placeholder = `__STR_${stringIndex++}__`;
                    strings.push({ placeholder, content: match });
                    return placeholder;
                });

                // 2. 安全的注释保护
                const comments = [];
                let commentIndex = 0;
                formatted = safeCommentProtection(formatted, comments, commentIndex);

                // 3. 基本格式化
                formatted = formatted
                    .replace(/\s+/g, ' ')
                    .replace(/\s*;\s*/g, ';\n')
                    .replace(/\s*{\s*/g, ' {\n')
                    .replace(/\s*}\s*/g, '\n}')
                    .trim();

                // 4. 恢复注释
                comments.forEach(({ placeholder, content }) => {
                    formatted = formatted.replace(placeholder, content);
                });

                // 5. 恢复字符串
                strings.forEach(({ placeholder, content }) => {
                    formatted = formatted.replace(placeholder, content);
                });

                // 6. 简单缩进
                const lines = formatted.split('\n');
                let indent = 0;
                const result = lines.map(line => {
                    line = line.trim();
                    if (!line) return '';
                    if (line.includes('}')) indent = Math.max(0, indent - 1);
                    const indented = '    '.repeat(indent) + line;
                    if (line.includes('{')) indent++;
                    return indented;
                }).join('\n');

                return result;
            } catch (error) {
                console.error('格式化失败:', error);
                return code;
            }
        }

        // 测试用例
        const testCases = [
            {
                name: '用户反馈的原始问题',
                code: `(data, params, refs) => {
    function sortByValueAndKey(arr) {
        return [...arr].sort((a, b) => {
            // 先按 value 排序  const valueCompare = a.TEMPLATE_CODE.localeCompare(b.TEMPLATE_CODE);
            if (valueCompare !== 0) return valueCompare;
            // 如果 value 相同，则按 key 排序  return a.EQUIPMENT_NAME - b.EQUIPMENT_NAME;
        });
    }
    return sortByValueAndKey(data)
}`,
                description: '测试注释后跟代码的情况'
            },
            {
                name: '多种注释混合',
                code: `// 单行注释1  let a = 1;
/* 多行注释 */ let b = 2;
// 单行注释2  const c = 3; let d = 4;`,
                description: '测试多种注释类型混合的情况'
            },
            {
                name: '注释在代码中间',
                code: `function test() { // 注释  return true; }`,
                description: '测试注释在代码中间的情况'
            }
        ];

        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '';
            
            let passCount = 0;
            
            testCases.forEach((testCase, index) => {
                const original = testCase.code;
                
                // 第一次格式化
                const firstFormat = formatJavaScriptFixed(original);
                
                // 第二次格式化（这里最容易出问题）
                const secondFormat = formatJavaScriptFixed(firstFormat);
                
                // 第三次格式化（验证稳定性）
                const thirdFormat = formatJavaScriptFixed(secondFormat);
                
                // 检查是否稳定（第二次和第三次应该相同）
                const isStable = secondFormat === thirdFormat;
                
                // 检查是否有代码丢失（简单检查关键字）
                const hasCodeLoss = original.includes('const') && !secondFormat.includes('const') ||
                                   original.includes('return') && !secondFormat.includes('return') ||
                                   original.includes('if') && !secondFormat.includes('if');
                
                const passed = isStable && !hasCodeLoss;
                if (passed) passCount++;
                
                const testDiv = document.createElement('div');
                testDiv.className = `result ${passed ? 'pass' : 'fail'}`;
                testDiv.innerHTML = `
                    <h4>${passed ? '✅' : '❌'} 测试 ${index + 1}: ${testCase.name}</h4>
                    <p>${testCase.description}</p>
                    <div><strong>原始代码:</strong></div>
                    <div class="code-block">${original}</div>
                    <div><strong>第一次格式化:</strong></div>
                    <div class="code-block">${firstFormat}</div>
                    <div><strong>第二次格式化:</strong></div>
                    <div class="code-block">${secondFormat}</div>
                    <div><strong>第三次格式化:</strong></div>
                    <div class="code-block">${thirdFormat}</div>
                    <div><strong>检查结果:</strong></div>
                    <div>
                        稳定性: ${isStable ? '✅ 通过' : '❌ 失败'}<br>
                        代码完整性: ${hasCodeLoss ? '❌ 有代码丢失' : '✅ 完整'}<br>
                        <strong>总体: ${passed ? '✅ 通过' : '❌ 失败'}</strong>
                    </div>
                `;
                resultsDiv.appendChild(testDiv);
            });
            
            // 总结
            const summary = document.createElement('div');
            summary.className = `result ${passCount === testCases.length ? 'pass' : 'fail'}`;
            summary.innerHTML = `
                <h3>${passCount === testCases.length ? '🎉' : '⚠️'} 测试总结</h3>
                <div>通过: ${passCount}/${testCases.length}</div>
                <div>状态: ${passCount === testCases.length ? '✅ 注释处理问题已修复' : '❌ 仍有问题需要修复'}</div>
            `;
            resultsDiv.insertBefore(summary, resultsDiv.firstChild);
        }

        // 事件绑定
        document.getElementById('runAllTests').addEventListener('click', runTests);
        document.getElementById('clearResults').addEventListener('click', () => {
            document.getElementById('testResults').innerHTML = '';
        });

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，自动运行注释处理测试...');
            runTests();
        });
    </script>
</body>
</html>
