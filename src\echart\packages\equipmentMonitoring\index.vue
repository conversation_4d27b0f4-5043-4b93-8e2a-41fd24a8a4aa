<template>
  <div :class="[b(), className]" :style="styleSizeName">
    <equipment-monitoring :option="option" />
  </div>
</template>

<script>
import EquipmentMonitoring from '@/components/equipmentMonitoring/index.vue'

export default {
  name: 'equipmentMonitoring',
  components: {
    EquipmentMonitoring
  },
  props: {
    option: {
      type: Object,
      default: () => ({})
    }
  }
}
</script>
