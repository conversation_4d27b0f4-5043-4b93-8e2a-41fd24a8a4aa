<template>
  <el-dialog :visible.sync="visible"
             :close-on-click-modal="false"
             :before-close="handleClose"
             :title="title || '数据处理'"
             width="80%">

    <div class="content">
      <monaco-editor ref="codeEditor"
                     v-model="code"
                     :options="editorOptions"></monaco-editor>
      <monaco-editor v-model="tip"
                     disabled></monaco-editor>
    </div>
    <span slot="footer"
          class="dialog-footer">
      <div class="footer-left">
        <el-button size="small"
                   type="success"
                   :icon="isFormatting ? 'el-icon-loading' : 'el-icon-magic-stick'"
                   :disabled="isFormatting"
                   :loading="isFormatting"
                   @click="formatCode">
                   {{ isFormatting ? '格式化中...' : '格式化代码' }}
        </el-button>
        <el-button size="small"
                   type="warning"
                   :icon="isValidating ? 'el-icon-loading' : 'el-icon-view'"
                   :disabled="isValidating"
                   :loading="isValidating"
                   @click="validateCode">
                   {{ isValidating ? '验证中...' : '验证语法' }}
        </el-button>
      </div>
      <div class="footer-right">
        <el-button size="small"
                   @click="setVisible(false)">
                   <!-- 取 消 -->
                  {{$t('other.CancelBtn')}}
                  </el-button>
        <el-button type="primary"
                   @click="submit"
                   size="small">
                   <!-- 确 定 -->
                   {{$t('other.ConfirmBtn')}}
                  </el-button>
      </div>
    </span>
  </el-dialog>
</template>

<script>
import { tip } from '@/config'
import { funEval } from '@/utils/utils'
import MonacoEditor from '@/page/components/editor'
export default {
  components: { MonacoEditor },
  data () {
    return {
      code: '',
      tip: '',
      isFormatting: false,  // 格式化状态标志
      isValidating: false,  // 验证状态标志
      formatClickCount: 0,  // 格式化点击计数
      editorOptions: {
        formatOnPaste: true,
        formatOnType: true,
        automaticLayout: true,
        minimap: { enabled: false },
        scrollBeyondLastLine: false,
        wordWrap: 'on',
        fontSize: 14,
        tabSize: 4,
        insertSpaces: true
      }
    }
  },
  props: {
    rules: {
      type: Boolean,
      default: true
    },
    title: String,
    visible: Boolean,
    type: String,
    value: [String, Object, Array]
  },
  watch: {
    value: {
      handler (val) {
        if (this.validatenull(val)) {
          if (['dataFormatter', 'stylesFormatter'].includes(this.type) && this.validatenull(val)) {
            this.code = `(data,params,refs)=>{
    return data
}`
          } else if (['query', 'header', 'dataQuery', 'dataHeader'].includes(this.type) && this.validatenull(val)) {
            this.code = `(data)=>{
    return data
}`
          } else if (['echartFormatter'].includes(this.type) && this.validatenull(val)) {
            this.code = `(data)=>{
    return data
}`
          } else if (['clickFormatter'].includes(this.type) && this.validatenull(val)) {
            this.code = `(params,refs)=>{
    console.log(params,refs)
}`
          } else if (['labelFormatter', 'formatter'].includes(this.type) && this.validatenull(val)) {
            this.code = `(name,data)=>{
    console.log(name,data)
    return ''
}`
          }
        } else {
          this.code = val;
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created () {
    this.tip = tip
  },
  methods: {
    // 格式化代码（优化版本，添加防重复点击保护）
    async formatCode() {
      try {
        // 防重复点击保护
        if (this.isFormatting) {
          this.formatClickCount++;
          this.$message.warning(`格式化正在进行中，请稍候（忽略第${this.formatClickCount}次点击）`);
          return;
        }

        // 检查代码是否为空
        if (!this.code || this.code.trim() === '') {
          this.$message.warning('代码内容为空，无法格式化');
          return;
        }

        this.isFormatting = true;
        this.formatClickCount = 1;

        // 显示加载提示
        const loading = this.$loading({
          lock: true,
          text: '正在格式化代码...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        try {
          // 使用Monaco编辑器的格式化功能
          if (this.$refs.codeEditor && this.$refs.codeEditor.formatCode) {
            await this.$refs.codeEditor.formatCode();
            this.$message.success('代码格式化成功');
          } else {
            // 回退到自定义格式化
            const formattedCode = this.formatJavaScript(this.code);
            this.code = formattedCode;
            this.$message.success('代码格式化成功（使用自定义格式化）');
          }
        } finally {
          loading.close();
          this.isFormatting = false;

          // 如果有重复点击，显示提示
          if (this.formatClickCount > 1) {
            this.$message.info(`格式化完成，期间忽略了${this.formatClickCount - 1}次重复点击`);
          }
          this.formatClickCount = 0;
        }
      } catch (error) {
        console.error('格式化失败:', error);
        this.$message.error(`代码格式化失败: ${error.message}`);
        this.isFormatting = false;
        this.formatClickCount = 0;
      }
    },

    // 验证代码语法（优化版本，添加防重复调用保护）
    async validateCode() {
      try {
        // 防重复验证保护
        if (this.isValidating) {
          this.$message.warning('语法验证正在进行中，请稍候');
          return;
        }

        if (!this.code || this.code.trim() === '') {
          this.$message.warning('代码内容为空，无法验证');
          return;
        }

        this.isValidating = true;

        // 显示加载提示
        const loading = this.$loading({
          lock: true,
          text: '正在验证语法...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        try {
          // 首先使用Monaco编辑器的语法检查
          if (this.$refs.codeEditor && this.$refs.codeEditor.validateSyntax) {
            await this.$refs.codeEditor.validateSyntax();
          }

          // 然后使用funEval进行运行时验证
          funEval(this.code);
          this.$message.success('代码语法验证通过');
        } finally {
          loading.close();
          this.isValidating = false;
        }
      } catch (error) {
        console.error('语法验证失败:', error);
        this.$message.error(`语法错误: ${error.message}`);
        this.isValidating = false;
      }
    },

    // 完全重新设计的JavaScript代码格式化方法 - 修复注释处理问题
    formatJavaScript(code) {
      if (!code || code.trim() === '') {
        return code;
      }

      try {
        // 1. 保护字符串内容
        const stringPlaceholders = [];
        let stringIndex = 0;
        let formatted = code.replace(/(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g, (match) => {
          const placeholder = `__STRING_${stringIndex++}__`;
          stringPlaceholders.push({ placeholder, content: match });
          return placeholder;
        });

        // 2. 保护注释内容（完全重写 - 安全的注释处理）
        const commentPlaceholders = [];
        let commentIndex = 0;

        // 使用更安全的方法处理注释，逐字符解析避免正则表达式的贪婪匹配问题
        formatted = this.safeCommentProtection(formatted, commentPlaceholders, commentIndex);

        // 3. 保护箭头函数
        const arrowPlaceholders = [];
        let arrowIndex = 0;
        formatted = formatted.replace(/\s*=>\s*/g, () => {
          const placeholder = `__ARROW_${arrowIndex++}__`;
          arrowPlaceholders.push({ placeholder, content: ' => ' });
          return placeholder;
        });

        // 4. 基本的空白处理
        formatted = formatted.replace(/\s+/g, ' ').trim();

        // 5. 处理操作符（完全重写，解决操作符分割问题）
        formatted = formatted
          // 使用更精确的匹配，确保操作符不被分割
          .replace(/([^=!<>])===([^=])/g, '$1 === $2')  // 严格相等
          .replace(/([^=!])!==([^=])/g, '$1 !== $2')    // 严格不等
          .replace(/([^=!<>])<=([^=])/g, '$1 <= $2')    // 小于等于
          .replace(/([^=!<>])>=([^=])/g, '$1 >= $2')    // 大于等于
          .replace(/([^=!<>])==([^=])/g, '$1 == $2')    // 相等
          .replace(/([^=!])!=([^=])/g, '$1 != $2')      // 不等
          .replace(/([^=!<>])<([^=])/g, '$1 < $2')      // 小于
          .replace(/([^=!<>])>([^=])/g, '$1 > $2')      // 大于
          .replace(/([^&])&&([^&])/g, '$1 && $2')       // 逻辑与
          .replace(/([^|])\|\|([^|])/g, '$1 || $2')     // 逻辑或
          // 算术操作符
          .replace(/([^+])\+([^+=])/g, '$1 + $2')       // 加法
          .replace(/([^-])-([^-=])/g, '$1 - $2')        // 减法
          .replace(/([^*])\*([^*=])/g, '$1 * $2')       // 乘法
          .replace(/([^%])%([^=])/g, '$1 % $2')         // 模运算
          // 赋值操作符
          .replace(/([a-zA-Z_$][a-zA-Z0-9_$]*|\]|\))=([^=])/g, '$1 = $2');

        // 5. 处理标点符号
        formatted = formatted
          // 逗号后加空格
          .replace(/,(?!\s)/g, ', ')
          // 分号后换行
          .replace(/;(?!\s*\n)/g, ';\n')
          // 大括号处理
          .replace(/\{(?!\s*\n)/g, ' {\n')
          .replace(/\}(?!\s*\n)/g, '\n}')
          // 小括号处理
          .replace(/\s*\(\s*/g, '(')
          .replace(/\s*\)\s*/g, ')');

        // 6. 最终清理（避免破坏已保护的内容）
        formatted = formatted
          .replace(/\s*{\s*/g, ' {\n')
          .replace(/\s*}\s*/g, '\n}')
          .replace(/\s*;\s*/g, ';\n')
          .replace(/\s*,\s*/g, ', ');

        // 7. 恢复箭头函数
        arrowPlaceholders.forEach(({ placeholder, content }) => {
          formatted = formatted.replace(placeholder, content);
        });

        // 8. 恢复注释内容
        commentPlaceholders.forEach(({ placeholder, content }) => {
          formatted = formatted.replace(placeholder, content);
        });

        // 9. 恢复字符串内容
        stringPlaceholders.forEach(({ placeholder, content }) => {
          formatted = formatted.replace(placeholder, content);
        });

        // 添加适当的缩进
        const lines = formatted.split('\n');
        let indentLevel = 0;
        const indentSize = 4; // 4个空格缩进

        const formattedLines = lines.map(line => {
          line = line.trim();
          if (line === '') return '';

          // 减少缩进（在处理行之前）
          if (line.includes('}')) {
            indentLevel = Math.max(0, indentLevel - 1);
          }

          const indentedLine = ' '.repeat(indentLevel * indentSize) + line;

          // 增加缩进（在处理行之后）
          if (line.includes('{')) {
            indentLevel++;
          }

          return indentedLine;
        });

        return formattedLines.join('\n');
      } catch (error) {
        console.error('格式化过程中出错:', error);
        return code; // 如果格式化失败，返回原始代码
      }
    },

    // 安全的注释保护方法 - 防止注释吞噬后续代码
    safeCommentProtection(code, commentPlaceholders, startIndex) {
      let result = '';
      let i = 0;
      let commentIndex = startIndex;

      while (i < code.length) {
        // 检查多行注释
        if (i < code.length - 1 && code[i] === '/' && code[i + 1] === '*') {
          let commentStart = i;
          i += 2; // 跳过 /*

          // 查找注释结束
          while (i < code.length - 1) {
            if (code[i] === '*' && code[i + 1] === '/') {
              i += 2; // 跳过 */
              break;
            }
            i++;
          }

          const comment = code.substring(commentStart, i);
          const placeholder = `__COMMENT_${commentIndex++}__`;
          commentPlaceholders.push({ placeholder, content: comment });
          result += placeholder;
          continue;
        }

        // 检查单行注释
        if (i < code.length - 1 && code[i] === '/' && code[i + 1] === '/') {
          let commentStart = i;
          i += 2; // 跳过 //

          // 查找注释结束 - 只到行尾或分号
          while (i < code.length) {
            const char = code[i];
            // 遇到换行符、分号、或者可能的代码结构就停止
            if (char === '\n' || char === '\r' ||
                char === ';' ||
                (char === ' ' && i + 1 < code.length && /[a-zA-Z_$]/.test(code[i + 1]))) {
              break;
            }
            i++;
          }

          const comment = code.substring(commentStart, i);
          const placeholder = `__COMMENT_${commentIndex++}__`;
          commentPlaceholders.push({ placeholder, content: comment });
          result += placeholder;
          continue;
        }

        // 普通字符
        result += code[i];
        i++;
      }

      return result;
    },

    // handleOpen () {
    //   this.form = this.value
    //   this.box = true;
    //   this.$nextTick(() => {
    //     this.handleOption()
    //   })
    // },
    handleClose () {
      this.setVisible(false);
    },
    submit () {
      let value = this.code;
      if (!this.rules) {
        this.$emit('submit', value);
        this.setVisible(false)
        return
      }
      try {
        funEval(value);
        if (['data'].includes(this.type)) value = funEval(value);
        this.$emit('submit', value);
        this.setVisible(false)
      } catch (error) {
        console.log(error);
        this.$message.error(`${this.$t('message.DataFormatIsIncorrect')}`)//'数据格式有误'
      }

    },
    setVisible (val) {
      this.$emit('update:visible', val);
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  .monaco_editor_container {
    flex: 1;
    &:first-child {
      flex: 2;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .footer-left {
    display: flex;
    gap: 10px;
  }

  .footer-right {
    display: flex;
    gap: 10px;
  }
}

// 格式化按钮样式
::v-deep .el-button--success {
  background-color: #67c23a;
  border-color: #67c23a;

  &:hover {
    background-color: #85ce61;
    border-color: #85ce61;
  }
}

::v-deep .el-button--warning {
  background-color: #e6a23c;
  border-color: #e6a23c;

  &:hover {
    background-color: #ebb563;
    border-color: #ebb563;
  }
}
</style>


