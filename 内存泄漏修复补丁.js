/**
 * CI.Web.Plugins.Bulletin 内存泄漏修复补丁
 * 
 * 使用方法：
 * 1. 将此文件放在项目根目录
 * 2. 在 src/main.js 中引入：import './内存泄漏修复补丁.js'
 * 3. 重新构建项目
 * 
 * 注意：这是临时修复方案，建议按照文档进行源码修复
 */

// 1. 修复表格组件内存泄漏
if (typeof window !== 'undefined' && window.Vue) {
  // 保存原始的 beforeDestroy 钩子
  const originalBeforeDestroy = window.Vue.config.optionMergeStrategies.beforeDestroy;
  
  // 扩展 Vue 组件的 beforeDestroy 钩子
  window.Vue.mixin({
    beforeDestroy() {
      // 清理表格滚动定时器
      if (this.scrollCheck) {
        clearInterval(this.scrollCheck);
        this.scrollCheck = null;
        console.log('清理表格滚动定时器');
      }
      
      // 清理其他定时器
      if (this.headerHeightTimer) {
        clearTimeout(this.headerHeightTimer);
        this.headerHeightTimer = null;
      }
      
      // 清理公共组件定时器
      if (this.checkChart) {
        clearInterval(this.checkChart);
        this.checkChart = null;
      }
      
      if (this.globalTimerCheck) {
        clearInterval(this.globalTimerCheck);
        this.globalTimerCheck = null;
      }
      
      if (this.appendCheck) {
        clearInterval(this.appendCheck);
        this.appendCheck = null;
      }
      
      if (this.timer_echartFormatter) {
        clearTimeout(this.timer_echartFormatter);
        this.timer_echartFormatter = null;
      }
      
      // 关闭WebSocket连接
      if (this.wsClient) {
        if (this.wsClient.readyState === WebSocket.OPEN || 
            this.wsClient.readyState === WebSocket.CONNECTING) {
          this.wsClient.close();
        }
        this.wsClient = null;
        console.log('关闭WebSocket连接');
      }
      
      // 清理ECharts实例
      if (this.myChart) {
        this.myChart.dispose();
        this.myChart = null;
        console.log('清理ECharts实例');
      }
      
      // 清理Vue组件渲染器的动态样式
      if (this.id && this.$options.name === 'vue') {
        const styleId = 'style-' + this.id;
        const existingStyle = document.getElementById(styleId);
        if (existingStyle) {
          existingStyle.remove();
          console.log('清理动态样式:', styleId);
        }
        
        // 清理动态注册的组件
        if (window.Vue.options.components && window.Vue.options.components[this.id]) {
          delete window.Vue.options.components[this.id];
          console.log('清理动态组件:', this.id);
        }
      }
    }
  });
}

// 2. 修复定时器管理
class TimerManager {
  constructor() {
    this.timers = new Set();
    this.intervals = new Set();
    this.patchTimerMethods();
  }
  
  patchTimerMethods() {
    const originalSetTimeout = window.setTimeout;
    const originalSetInterval = window.setInterval;
    const originalClearTimeout = window.clearTimeout;
    const originalClearInterval = window.clearInterval;
    
    window.setTimeout = (fn, delay, ...args) => {
      const id = originalSetTimeout(fn, delay, ...args);
      this.timers.add(id);
      return id;
    };
    
    window.setInterval = (fn, delay, ...args) => {
      const id = originalSetInterval(fn, delay, ...args);
      this.intervals.add(id);
      return id;
    };
    
    window.clearTimeout = (id) => {
      this.timers.delete(id);
      return originalClearTimeout(id);
    };
    
    window.clearInterval = (id) => {
      this.intervals.delete(id);
      return originalClearInterval(id);
    };
  }
  
  clearAll() {
    this.timers.forEach(id => clearTimeout(id));
    this.intervals.forEach(id => clearInterval(id));
    this.timers.clear();
    this.intervals.clear();
    console.log('清理所有定时器');
  }
  
  getStats() {
    return {
      activeTimers: this.timers.size,
      activeIntervals: this.intervals.size
    };
  }
}

// 创建全局定时器管理器
window.timerManager = new TimerManager();

// 3. 内存监控
class MemoryMonitor {
  constructor() {
    this.isMonitoring = false;
    this.monitorInterval = null;
    this.memoryHistory = [];
  }
  
  start() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.monitorInterval = setInterval(() => {
      if (performance.memory) {
        const memory = {
          timestamp: Date.now(),
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit
        };
        
        this.memoryHistory.push(memory);
        
        // 保持最近100条记录
        if (this.memoryHistory.length > 100) {
          this.memoryHistory.shift();
        }
        
        // 检查内存使用率
        const usagePercent = (memory.used / memory.limit) * 100;
        if (usagePercent > 80) {
          console.warn(`⚠️ 内存使用率过高: ${usagePercent.toFixed(2)}%`);
          console.warn('定时器统计:', window.timerManager.getStats());
        }
        
        // 检查内存增长趋势
        if (this.memoryHistory.length >= 10) {
          const recent = this.memoryHistory.slice(-10);
          const growth = recent[recent.length - 1].used - recent[0].used;
          const growthMB = growth / 1024 / 1024;
          
          if (growthMB > 50) { // 50MB增长
            console.warn(`⚠️ 检测到内存快速增长: ${growthMB.toFixed(2)}MB`);
          }
        }
      }
    }, 10000); // 每10秒检查一次
  }
  
  stop() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    this.isMonitoring = false;
  }
  
  getReport() {
    if (this.memoryHistory.length === 0) return null;
    
    const latest = this.memoryHistory[this.memoryHistory.length - 1];
    const usedMB = (latest.used / 1024 / 1024).toFixed(2);
    const totalMB = (latest.total / 1024 / 1024).toFixed(2);
    const limitMB = (latest.limit / 1024 / 1024).toFixed(2);
    
    return {
      current: `${usedMB}MB / ${totalMB}MB`,
      limit: `${limitMB}MB`,
      usage: `${((latest.used / latest.limit) * 100).toFixed(2)}%`,
      history: this.memoryHistory.length
    };
  }
}

// 创建全局内存监控器
window.memoryMonitor = new MemoryMonitor();

// 4. 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  console.log('页面卸载，开始清理资源...');
  
  // 清理所有定时器
  window.timerManager.clearAll();
  
  // 停止内存监控
  window.memoryMonitor.stop();
  
  // 清理所有WebSocket连接
  if (window.WebSocket) {
    // 这里可以添加全局WebSocket连接清理逻辑
  }
  
  console.log('资源清理完成');
});

// 5. 开发环境下启动监控
if (process.env.NODE_ENV === 'development') {
  window.memoryMonitor.start();
  
  // 添加全局调试方法
  window.debugMemory = () => {
    console.log('内存报告:', window.memoryMonitor.getReport());
    console.log('定时器统计:', window.timerManager.getStats());
  };
  
  console.log('内存泄漏修复补丁已加载');
  console.log('使用 debugMemory() 查看内存状态');
}

// 6. 错误处理
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error);
  
  // 如果是内存相关错误，尝试清理资源
  if (event.error && event.error.message && 
      event.error.message.includes('memory')) {
    console.log('检测到内存错误，尝试清理资源...');
    window.timerManager.clearAll();
  }
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise拒绝:', event.reason);
});

export default {
  timerManager: window.timerManager,
  memoryMonitor: window.memoryMonitor
};
