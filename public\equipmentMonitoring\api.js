// This file simulates a backend API call

const mockDeviceData = [
    // Top Row ->
    { id: 'dev-01', name: '放板机', x: 50, y: 50, status: 'running', connections: ['dev-02'], tooltipData: { '型号': 'LDR-2000', '厂商': 'A-Tech', '投入日期': '2022-08-15' } },
    { id: 'dev-02', name: '激光打码', x: 200, y: 50, status: 'running', connections: ['dev-03'], icons: { camera: true }, tooltipData: { '型号': 'LM-500', '功率': '50W', '厂商': 'B-Laser' } },
    { id: 'dev-03', name: '线路前处理', x: 350, y: 50, status: 'running', connections: ['dev-04'], tooltipData: { '处理方式': '等离子清洗', '厂商': 'C-Process' } },
    { id: 'dev-04', name: '暂存机', x: 500, y: 50, status: 'idle', connections: ['dev-05'] },
    { id: 'dev-05', name: '整平机', x: 650, y: 50, status: 'running', connections: ['dev-06'] },
    { id: 'dev-06', name: '涂布机', x: 800, y: 50, status: 'running', connections: ['dev-07'] },
    { id: 'dev-07', name: '隧道炉', x: 950, y: 50, status: 'running', connections: ['dev-08'] },
    { id: 'dev-08', name: '整平机', x: 1100, y: 50, status: 'running', connections: ['dev-09'] },
    { id: 'dev-09', name: '暂存机', x: 1250, y: 50, status: 'idle', connections: ['dev-10'] },
    { id: 'dev-10', name: '线路DI', x: 1400, y: 50, status: 'running', connections: ['dev-11'], icons: { camera: true } },
    { id: 'dev-11', name: 'NG暂存机', x: 1550, y: 50, status: 'idle', connections: ['dev-12'] },
    { id: 'dev-12', name: '显影蚀刻退膜', x: 1700, y: 50, status: 'running', connections: ['dev-13'], icons: { star: true } },
    { id: 'dev-13', name: 'AOI', x: 1850, y: 50, status: 'running', connections: ['dev-14', 'dev-13-alt'] },

    // Right Column, Main Flow (Down)
    { id: 'dev-14', name: '转角机1', x: 1850, y: 180, status: 'running', connections: ['dev-15', 'dev-18'] },
    { id: 'dev-15', name: '侧边一体机', x: 1850, y: 310, status: 'running', connections: ['dev-16'] },
    { id: 'dev-16', name: '打靶', x: 1850, y: 440, status: 'running', connections: ['dev-17'] },
    { id: 'dev-17', name: '转角机3', x: 1850, y: 570, status: 'running', connections: ['dev-22'] },

    // Right Column, Parallel Flow (Down)
    { id: 'dev-18', name: '转角机2', x: 2000, y: 180, status: 'running', connections: ['dev-19'] },
    { id: 'dev-19', name: '侧边一体机', x: 2000, y: 310, status: 'running', connections: ['dev-20'] },
    { id: 'dev-20', name: '打靶', x: 2000, y: 440, status: 'running', connections: ['dev-21'] },
    { id: 'dev-21', name: '转角机4', x: 2000, y: 570, status: 'idle', connections: ['dev-22'] },

    // AOI检修 Fork
    // { id: 'dev-13-alt', name: 'AOI检修', x: 1700, y: 180, status: 'idle', connections: ['dev-14'] },
    { id: 'dev-14-alt', name: 'AOI检修', x: 1700, y: 310, status: 'idle', connections: ['dev-19'] },
    // Bottom Row <-
    { id: 'dev-22', name: '阻焊前处理', x: 1700, y: 570, status: 'running', connections: ['dev-23'] },
    { id: 'dev-23', name: '暂存机', x: 1550, y: 570, status: 'idle', connections: ['dev-24'] },
    { id: 'dev-24', name: '阻焊涂布', x: 1400, y: 570, status: 'running', connections: ['dev-25'] },
    { id: 'dev-25', name: '隧道炉', x: 1250, y: 570, status: 'running', connections: ['dev-26'] },
    { id: 'dev-26', name: '先进先出暂存机', x: 1100, y: 570, status: 'idle', connections: ['dev-27'] },
    { id: 'dev-27', name: '暂存机', x: 950, y: 570, status: 'running', connections: ['dev-28'], icons: { camera: true } },
    { id: 'dev-28', name: '阻焊DI', x: 800, y: 570, status: 'idle', connections: ['dev-29'] },
    { id: 'dev-29', name: 'NG暂存机', x: 650, y: 570, status: 'running', connections: ['dev-30'], icons: { star: true } },
    { id: 'dev-30', name: '显影', x: 500, y: 570, status: 'idle', connections: ['dev-31'] },
    { id: 'dev-31', name: '暂存机', x: 500, y: 440, status: 'idle', connections: ['dev-32'] },
    { id: 'dev-32', name: '文字', x: 350, y: 440, status: 'running', connections: ['dev-33'] },
    { id: 'dev-33', name: '隧道炉', x: 200, y: 440, status: 'running', connections: ['dev-34'] },
    { id: 'dev-34', name: '收板机', x: 50, y: 440, status: 'running', connections: [] }
];

// --- Mock Data Generation (Fallback) ---
function getMockDeviceData() {
    return new Promise(resolve => {
        console.warn("API fetch failed. Falling back to mock data.");
        setTimeout(() => {
            const dynamicData = JSON.parse(JSON.stringify(mockDeviceData));
            dynamicData.forEach(device => {
                const rand = Math.random();
                if (rand < 0.15) { device.status = 'alarm'; }
                else if (rand < 0.4) { device.status = 'idle'; }
                else { device.status = 'running'; }
            });
            resolve(dynamicData);
        }, 500);
    });
}

// --- Real API Call ---
async function getDeviceData() {
    return fetch('api/getDeviceData')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error("Could not fetch real data:", error);
            // Fallback to mock data if the real API fails
            return getMockDeviceData();
        });
}
