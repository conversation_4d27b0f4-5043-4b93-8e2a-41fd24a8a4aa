<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备监控台样式对比测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #0a0a0a;
            color: #dcdcdc;
        }
        
        .comparison-container {
            display: flex;
            gap: 20px;
            height: 80vh;
        }
        
        .panel {
            flex: 1;
            border: 1px solid #333;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }
        
        .panel-title {
            background-color: #1a1a2e;
            color: #e94560;
            padding: 10px 20px;
            text-align: center;
            font-weight: bold;
            border-bottom: 1px solid #0f3460;
        }
        
        .panel-content {
            height: calc(100% - 50px);
            position: relative;
        }
        
        /* 原插件样式 */
        .original-style {
            background-color: #1a1a2e;
            color: #dcdcdc;
            overflow: hidden;
            padding: 20px 40px 20px 20px;
        }
        
        .original-style .device-layout {
            position: relative;
            flex-grow: 1;
            background-color: #16213e;
            border: 1px solid #0f3460;
            border-radius: 10px;
            background-image: radial-gradient(circle, #0f3460 1px, transparent 1px);
            background-size: 20px 20px;
            overflow: hidden;
            margin-left: 20px;
            height: 100%;
        }
        
        .original-style .device {
            position: absolute;
            width: 120px;
            height: 70px;
            background-color: #0f3460;
            border: 2px solid #3498db;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
            color: #dcdcdc;
        }
        
        .original-style .device:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
        }
        
        .original-style .device.running {
            border-color: #2ecc71;
        }
        
        .original-style .device.alarm {
            border-color: #e74c3c;
            animation: pulse-alarm 1s infinite;
        }
        
        .original-style .device-name {
            font-weight: 500;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }
        
        .original-style .device-icons {
            position: absolute;
            top: 5px;
            right: 5px;
            display: flex;
            gap: 5px;
        }
        
        .original-style .device-icons i {
            font-size: 0.8rem;
            opacity: 0.7;
        }
        
        .original-style .flow-dot {
            position: absolute;
            width: 8px;
            height: 8px;
            background-color: #2ecc71;
            border-radius: 50%;
            box-shadow: 0 0 5px #2ecc71, 0 0 10px #2ecc71;
            opacity: 0;
            animation: flow 4s linear infinite;
        }
        
        /* 控制面板 */
        .controls {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(26, 26, 46, 0.9);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #0f3460;
            z-index: 100;
        }
        
        .control-group {
            margin-bottom: 10px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            color: #e94560;
            font-size: 12px;
        }
        
        .control-group input, .control-group select {
            width: 100%;
            padding: 5px;
            border: 1px solid #0f3460;
            border-radius: 4px;
            background-color: #16213e;
            color: #dcdcdc;
            font-size: 12px;
        }
        
        .control-group button {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 4px;
            background-color: #e94560;
            color: white;
            cursor: pointer;
            font-size: 12px;
        }
        
        .control-group button:hover {
            background-color: #d63447;
        }
        
        /* 动画 */
        @keyframes pulse-alarm {
            0% {
                box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
            }
        }
        
        @keyframes flow {
            0% {
                opacity: 1;
                offset-distance: 0%;
            }
            90% {
                opacity: 1;
            }
            100% {
                opacity: 0;
                offset-distance: 100%;
            }
        }
        
        h1 {
            color: #e94560;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 0 0 10px #e94560;
        }
        
        .transparency-demo {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            animation: gradientShift 4s ease infinite;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
    </style>
</head>
<body>
    <h1>设备监控台样式对比测试</h1>
    
    <div class="comparison-container">
        <!-- 原插件效果 -->
        <div class="panel">
            <div class="panel-title">原插件效果（参考）</div>
            <div class="panel-content original-style">
                <div class="device-layout" id="original-layout">
                    <div id="original-zoom-container">
                        <!-- 设备将由JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 透明度测试 -->
        <div class="panel transparency-demo">
            <div class="panel-title">背景透明度测试</div>
            <div class="panel-content original-style" id="transparency-panel">
                <div class="controls">
                    <div class="control-group">
                        <label>背景透明度:</label>
                        <input type="range" id="opacitySlider" min="0" max="1" step="0.1" value="1">
                        <span id="opacityValue">1.0</span>
                    </div>
                    <div class="control-group">
                        <button onclick="toggleAnimation()">切换动画</button>
                    </div>
                    <div class="control-group">
                        <button onclick="addRandomDevice()">添加设备</button>
                    </div>
                </div>
                <div class="device-layout" id="transparency-layout" style="background-color: rgba(22, 33, 62, 1);">
                    <div id="transparency-zoom-container">
                        <!-- 设备将由JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../public/equipmentMonitoring/api.js"></script>
    <script>
        let devices = [];
        let animationEnabled = true;
        
        // 初始化原插件效果
        function initOriginalDemo() {
            getDeviceData().then(deviceData => {
                devices = deviceData.slice(0, 8); // 只显示前8个设备
                renderDevices('original-zoom-container', devices);
                renderConnections('original-zoom-container', devices);
            });
        }
        
        // 初始化透明度测试
        function initTransparencyDemo() {
            const transparencyDevices = [
                { id: 'dev-01', name: '放板机', x: 50, y: 50, status: 'running', connections: ['dev-02'] },
                { id: 'dev-02', name: '激光打码', x: 200, y: 50, status: 'running', connections: ['dev-03'], icons: { camera: true } },
                { id: 'dev-03', name: '线路前处理', x: 350, y: 50, status: 'idle', connections: ['dev-04'] },
                { id: 'dev-04', name: '暂存机', x: 500, y: 50, status: 'alarm', connections: [] }
            ];
            renderDevices('transparency-zoom-container', transparencyDevices);
            renderConnections('transparency-zoom-container', transparencyDevices);
        }
        
        // 渲染设备
        function renderDevices(containerId, deviceList) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            
            deviceList.forEach(device => {
                const devElement = document.createElement('div');
                devElement.className = `device ${device.status}`;
                devElement.id = `${containerId}_${device.id}`;
                devElement.style.left = `${device.x}px`;
                devElement.style.top = `${device.y}px`;
                
                const name = document.createElement('div');
                name.className = 'device-name';
                name.textContent = device.name;
                
                const icons = document.createElement('div');
                icons.className = 'device-icons';
                let iconHtml = '<i class="fa-solid fa-triangle-exclamation" style="display: none; color: #e74c3c;"></i>';
                if (device.icons?.camera) {
                    iconHtml += '<i class="fa-solid fa-video" style="color: #3498db;"></i>';
                }
                if (device.icons?.star) {
                    iconHtml += '<i class="fa-solid fa-star" style="color: #f1c40f;"></i>';
                }
                icons.innerHTML = iconHtml;
                
                // 显示告警图标
                if (device.status === 'alarm') {
                    const alarmIcon = icons.querySelector('.fa-triangle-exclamation');
                    if (alarmIcon) alarmIcon.style.display = 'inline-block';
                }
                
                devElement.appendChild(name);
                devElement.appendChild(icons);
                container.appendChild(devElement);
            });
        }
        
        // 渲染连接线和流动点
        function renderConnections(containerId, deviceList) {
            const container = document.getElementById(containerId);
            const svgNS = "http://www.w3.org/2000/svg";
            const svg = document.createElementNS(svgNS, 'svg');
            svg.setAttribute('width', '100%');
            svg.setAttribute('height', '100%');
            svg.style.position = 'absolute';
            svg.style.top = '0';
            svg.style.left = '0';
            svg.style.zIndex = '-1';
            
            deviceList.forEach(startDevice => {
                if (startDevice.connections) {
                    startDevice.connections.forEach(endDeviceId => {
                        const endDevice = deviceList.find(d => d.id === endDeviceId);
                        if (!endDevice) return;
                        
                        const startEl = document.getElementById(`${containerId}_${startDevice.id}`);
                        const endEl = document.getElementById(`${containerId}_${endDevice.id}`);
                        if (!startEl || !endEl) return;
                        
                        const line = document.createElementNS(svgNS, 'path');
                        const startX = startEl.offsetLeft + startEl.offsetWidth / 2;
                        const startY = startEl.offsetTop + startEl.offsetHeight / 2;
                        const endX = endEl.offsetLeft + endEl.offsetWidth / 2;
                        const endY = endEl.offsetTop + endEl.offsetHeight / 2;
                        
                        const d = `M ${startX} ${startY} L ${endX} ${endY}`;
                        line.setAttribute('d', d);
                        line.setAttribute('stroke', '#537895');
                        line.setAttribute('stroke-width', '2');
                        line.setAttribute('fill', 'none');
                        svg.appendChild(line);
                        
                        // 添加流动点
                        if (animationEnabled) {
                            const dot = document.createElement('div');
                            dot.className = 'flow-dot';
                            dot.style.offsetPath = `path('${d}')`;
                            dot.style.animationDelay = `${Math.random() * 4}s`;
                            container.appendChild(dot);
                        }
                    });
                }
            });
            
            container.appendChild(svg);
        }
        
        // 透明度控制
        document.getElementById('opacitySlider').addEventListener('input', function(e) {
            const opacity = e.target.value;
            document.getElementById('opacityValue').textContent = opacity;
            const layout = document.getElementById('transparency-layout');
            layout.style.backgroundColor = `rgba(22, 33, 62, ${opacity})`;
        });
        
        // 切换动画
        function toggleAnimation() {
            animationEnabled = !animationEnabled;
            initTransparencyDemo(); // 重新渲染
        }
        
        // 添加随机设备
        function addRandomDevice() {
            const newDevice = {
                id: `dev-${Date.now()}`,
                name: `设备${Math.floor(Math.random() * 100)}`,
                x: Math.random() * 400 + 50,
                y: Math.random() * 200 + 50,
                status: ['running', 'idle', 'alarm'][Math.floor(Math.random() * 3)],
                connections: [],
                icons: Math.random() > 0.5 ? { camera: true } : {}
            };
            
            const transparencyDevices = [
                { id: 'dev-01', name: '放板机', x: 50, y: 50, status: 'running', connections: ['dev-02'] },
                { id: 'dev-02', name: '激光打码', x: 200, y: 50, status: 'running', connections: ['dev-03'], icons: { camera: true } },
                { id: 'dev-03', name: '线路前处理', x: 350, y: 50, status: 'idle', connections: ['dev-04'] },
                { id: 'dev-04', name: '暂存机', x: 500, y: 50, status: 'alarm', connections: [] },
                newDevice
            ];
            
            renderDevices('transparency-zoom-container', transparencyDevices);
            renderConnections('transparency-zoom-container', transparencyDevices);
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initOriginalDemo();
            initTransparencyDemo();
        });
    </script>
</body>
</html>
