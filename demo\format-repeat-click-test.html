<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重复点击格式化代码测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
            padding: 20px;
        }
        .editor-container {
            border: 1px solid #ccc;
            border-radius: 4px;
            margin: 10px 0;
        }
        .editor {
            width: 100%;
            height: 200px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            padding: 10px;
            border: none;
            resize: vertical;
            background: #1e1e1e;
            color: #d4d4d4;
        }
        .button-group {
            margin: 10px 0;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        .btn-format {
            background: #67c23a;
            color: white;
        }
        .btn-format:hover {
            background: #85ce61;
        }
        .btn-format:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }
        .btn-test {
            background: #409eff;
            color: white;
        }
        .btn-test:hover {
            background: #66b1ff;
        }
        .btn-clear {
            background: #f56c6c;
            color: white;
        }
        .btn-clear:hover {
            background: #f78989;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.idle {
            background: #e1f3d8;
            color: #67c23a;
        }
        .status.formatting {
            background: #fdf6ec;
            color: #e6a23c;
        }
        .status.error {
            background: #fef0f0;
            color: #f56c6c;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
            border-bottom: 1px solid #eee;
        }
        .log-entry:last-child {
            border-bottom: none;
        }
        .timestamp {
            color: #666;
            font-size: 11px;
        }
        .counter {
            background: #909399;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 10px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .test-result.pass {
            background: #e1f3d8;
            color: #67c23a;
        }
        .test-result.fail {
            background: #fef0f0;
            color: #f56c6c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 重复点击格式化代码测试</h1>
        <p>测试重复快速点击格式化按钮时可能出现的问题</p>

        <div class="test-section">
            <h2>测试1: 模拟当前实现（有问题的版本）</h2>
            <div class="editor-container">
                <textarea id="editor1" class="editor" placeholder="在这里输入JavaScript代码...">(data, params, refs) => {
    function sortByValueAndKey(arr) {
        return [...arr].sort((a, b) => {
            // 先按 value 排序  const valueCompare = a.TEMPLATE_CODE.localeCompare(b.TEMPLATE_CODE); if(valueCompare !== 0)return valueCompare;
        });
    }
    return sortByValueAndKey(data)
}</textarea>
            </div>
            <div class="button-group">
                <button id="formatBtn1" class="btn btn-format">格式化代码</button>
                <button id="rapidTestBtn1" class="btn btn-test">快速连击测试 (10次)</button>
                <button id="clearBtn1" class="btn btn-clear">清空日志</button>
                <span id="counter1" class="counter">点击次数: 0</span>
            </div>
            <div id="status1" class="status idle">状态: 空闲</div>
            <div id="log1" class="log"></div>
        </div>

        <div class="test-section">
            <h2>测试2: 优化后的实现（修复版本）</h2>
            <div class="editor-container">
                <textarea id="editor2" class="editor" placeholder="在这里输入JavaScript代码...">(data, params, refs) => {
    function sortByValueAndKey(arr) {
        return [...arr].sort((a, b) => {
            // 先按 value 排序  const valueCompare = a.TEMPLATE_CODE.localeCompare(b.TEMPLATE_CODE); if(valueCompare !== 0)return valueCompare;
        });
    }
    return sortByValueAndKey(data)
}</textarea>
            </div>
            <div class="button-group">
                <button id="formatBtn2" class="btn btn-format">格式化代码</button>
                <button id="rapidTestBtn2" class="btn btn-test">快速连击测试 (10次)</button>
                <button id="clearBtn2" class="btn btn-clear">清空日志</button>
                <span id="counter2" class="counter">点击次数: 0</span>
            </div>
            <div id="status2" class="status idle">状态: 空闲</div>
            <div id="log2" class="log"></div>
        </div>

        <div class="test-section">
            <h2>测试结果对比</h2>
            <div id="testResults"></div>
        </div>
    </div>

    <script>
        // 日志记录函数
        function log(containerId, message, type = 'info') {
            const logContainer = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新状态
        function updateStatus(statusId, message, type = 'idle') {
            const statusElement = document.getElementById(statusId);
            statusElement.textContent = `状态: ${message}`;
            statusElement.className = `status ${type}`;
        }

        // 更新计数器
        function updateCounter(counterId, count) {
            document.getElementById(counterId).textContent = `点击次数: ${count}`;
        }

        // 清空日志
        function clearLog(logId) {
            document.getElementById(logId).innerHTML = '';
        }

        // 格式化JavaScript代码的函数（简化版，用于测试）
        function formatJavaScript(code) {
            if (!code || code.trim() === '') return code;
            
            try {
                // 简单的格式化逻辑
                let formatted = code
                    .replace(/\s+/g, ' ')
                    .replace(/\s*{\s*/g, ' {\n    ')
                    .replace(/\s*}\s*/g, '\n}')
                    .replace(/\s*;\s*/g, ';\n    ')
                    .replace(/\s*,\s*/g, ', ')
                    .replace(/\s*=>\s*/g, ' => ')
                    .replace(/\s*===\s*/g, ' === ')
                    .replace(/\s*!==\s*/g, ' !== ')
                    .replace(/\s*<=\s*/g, ' <= ')
                    .replace(/\s*>=\s*/g, ' >= ')
                    .replace(/\s*&&\s*/g, ' && ')
                    .replace(/\s*\|\|\s*/g, ' || ');
                
                // 简单的缩进处理
                const lines = formatted.split('\n');
                let indent = 0;
                const result = lines.map(line => {
                    line = line.trim();
                    if (!line) return '';
                    if (line.includes('}')) indent = Math.max(0, indent - 1);
                    const indented = '    '.repeat(indent) + line;
                    if (line.includes('{')) indent++;
                    return indented;
                }).join('\n');
                
                return result;
            } catch (error) {
                throw new Error(`格式化失败: ${error.message}`);
            }
        }

        // 模拟异步延迟
        function delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 测试1: 有问题的实现（模拟当前代码的问题）
        let formatCount1 = 0;
        let isFormatting1 = false;

        async function formatCodeProblematic(editorId, statusId, logId, counterId) {
            formatCount1++;
            updateCounter(counterId, formatCount1);

            log(logId, `开始格式化 #${formatCount1}`, 'info');

            // 没有防重复点击的保护
            updateStatus(statusId, '正在格式化...', 'formatting');

            try {
                // 模拟格式化过程
                await delay(500 + Math.random() * 500); // 随机延迟

                const editor = document.getElementById(editorId);
                const code = editor.value;
                const formatted = formatJavaScript(code);
                editor.value = formatted;

                updateStatus(statusId, '格式化完成', 'idle');
                log(logId, `格式化完成 #${formatCount1}`, 'success');

            } catch (error) {
                updateStatus(statusId, `格式化失败: ${error.message}`, 'error');
                log(logId, `格式化失败 #${formatCount1}: ${error.message}`, 'error');
            }
        }

        // 测试2: 优化后的实现（修复版本）
        let formatCount2 = 0;
        let isFormatting2 = false;
        let formatQueue2 = 0;

        async function formatCodeOptimized(editorId, statusId, logId, counterId) {
            formatCount2++;
            updateCounter(counterId, formatCount2);

            // 防重复点击保护
            if (isFormatting2) {
                formatQueue2++;
                log(logId, `格式化请求 #${formatCount2} 被忽略（正在格式化中，队列: ${formatQueue2}）`, 'warning');
                return;
            }

            isFormatting2 = true;
            const formatBtn = document.getElementById('formatBtn2');
            formatBtn.disabled = true;

            log(logId, `开始格式化 #${formatCount2}`, 'info');
            updateStatus(statusId, '正在格式化...', 'formatting');

            try {
                // 模拟格式化过程
                await delay(500 + Math.random() * 500); // 随机延迟

                const editor = document.getElementById(editorId);
                const code = editor.value;
                const formatted = formatJavaScript(code);
                editor.value = formatted;

                updateStatus(statusId, '格式化完成', 'idle');
                log(logId, `格式化完成 #${formatCount2}`, 'success');

                if (formatQueue2 > 0) {
                    log(logId, `忽略了 ${formatQueue2} 个重复请求`, 'info');
                    formatQueue2 = 0;
                }

            } catch (error) {
                updateStatus(statusId, `格式化失败: ${error.message}`, 'error');
                log(logId, `格式化失败 #${formatCount2}: ${error.message}`, 'error');
            } finally {
                isFormatting2 = false;
                formatBtn.disabled = false;
            }
        }

        // 快速连击测试
        async function rapidClickTest(formatFunction, editorId, statusId, logId, counterId) {
            log(logId, '开始快速连击测试...', 'info');

            for (let i = 0; i < 10; i++) {
                formatFunction(editorId, statusId, logId, counterId);
                await delay(50); // 50ms间隔快速点击
            }

            log(logId, '快速连击测试完成', 'info');
        }

        // 绑定事件
        document.getElementById('formatBtn1').addEventListener('click', () => {
            formatCodeProblematic('editor1', 'status1', 'log1', 'counter1');
        });

        document.getElementById('rapidTestBtn1').addEventListener('click', () => {
            rapidClickTest(formatCodeProblematic, 'editor1', 'status1', 'log1', 'counter1');
        });

        document.getElementById('clearBtn1').addEventListener('click', () => {
            clearLog('log1');
            formatCount1 = 0;
            updateCounter('counter1', 0);
        });

        document.getElementById('formatBtn2').addEventListener('click', () => {
            formatCodeOptimized('editor2', 'status2', 'log2', 'counter2');
        });

        document.getElementById('rapidTestBtn2').addEventListener('click', () => {
            rapidClickTest(formatCodeOptimized, 'editor2', 'status2', 'log2', 'counter2');
        });

        document.getElementById('clearBtn2').addEventListener('click', () => {
            clearLog('log2');
            formatCount2 = 0;
            formatQueue2 = 0;
            updateCounter('counter2', 0);
        });

        // 页面加载完成后显示说明
        document.addEventListener('DOMContentLoaded', function() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = `
                <div class="test-result">
                    <h3>测试说明</h3>
                    <p><strong>测试1（有问题的版本）</strong>：模拟当前代码的问题，没有防重复点击保护</p>
                    <ul>
                        <li>可能出现多个格式化操作同时进行</li>
                        <li>状态管理混乱</li>
                        <li>可能导致代码被多次格式化，产生不可预期的结果</li>
                    </ul>
                    <p><strong>测试2（优化版本）</strong>：添加了防重复点击保护和状态管理</p>
                    <ul>
                        <li>同时只能有一个格式化操作进行</li>
                        <li>重复点击会被忽略并记录</li>
                        <li>按钮状态正确管理</li>
                        <li>清晰的日志记录</li>
                    </ul>
                    <p><strong>使用方法</strong>：</p>
                    <ol>
                        <li>点击"格式化代码"按钮测试单次格式化</li>
                        <li>点击"快速连击测试"按钮模拟用户快速重复点击</li>
                        <li>观察两个版本的不同表现</li>
                        <li>查看日志了解详细执行过程</li>
                    </ol>
                </div>
            `;
        });
    </script>
</body>
</html>
