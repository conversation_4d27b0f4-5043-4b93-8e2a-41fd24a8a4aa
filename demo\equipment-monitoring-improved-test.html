<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备监控台组件 - 改进版测试</title>
    <script src="../dist/CI.Web.Plugins.Bulletin.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #0a0a0a;
            color: #dcdcdc;
        }
        .container {
            width: 100%;
            height: 90vh;
            border: 1px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #1a1a2e;
            border-radius: 8px;
            border: 1px solid #0f3460;
        }
        .control-group {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        .control-group label {
            display: block;
            margin-bottom: 5px;
            color: #e94560;
            font-weight: 500;
        }
        .control-group input, .control-group select {
            padding: 5px 10px;
            border: 1px solid #0f3460;
            border-radius: 4px;
            background-color: #16213e;
            color: #dcdcdc;
        }
        .control-group button {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            background-color: #e94560;
            color: white;
            cursor: pointer;
            font-weight: 500;
        }
        .control-group button:hover {
            background-color: #d63447;
        }
        h1 {
            color: #e94560;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 0 0 10px #e94560;
        }
    </style>
</head>
<body>
    <h1>设备监控台组件 - 改进版测试</h1>
    
    <div class="controls">
        <div class="control-group">
            <label>背景透明度:</label>
            <input type="range" id="backgroundOpacity" min="0" max="1" step="0.1" value="1">
            <span id="opacityValue">1.0</span>
        </div>
        
        <div class="control-group">
            <label>背景颜色:</label>
            <input type="color" id="backgroundColor" value="#1a1a2e">
        </div>
        
        <div class="control-group">
            <label>布局背景颜色:</label>
            <input type="color" id="layoutBackgroundColor" value="#16213e">
        </div>
        
        <div class="control-group">
            <label>标题颜色:</label>
            <input type="color" id="headerColor" value="#e94560">
        </div>
        
        <div class="control-group">
            <label>显示标题:</label>
            <input type="checkbox" id="showHeader" checked>
        </div>
        
        <div class="control-group">
            <button onclick="updateComponent()">更新组件</button>
        </div>
        
        <div class="control-group">
            <button onclick="togglePolling()">切换轮询</button>
        </div>
    </div>

    <div class="container" id="container"></div>

    <script>
        let componentInstance = null;
        let pollingEnabled = true;

        // 初始化组件配置
        const initialOption = {
            showHeader: true,
            title: '产线设备实时监控',
            backgroundColor: '#1a1a2e',
            layoutBackgroundColor: '#16213e',
            backgroundOpacity: 1,
            textColor: '#dcdcdc',
            headerColor: '#e94560',
            headerFontSize: '2.5rem',
            headerAlign: 'center',
            fontFamily: 'Noto Sans SC, sans-serif',
            enablePolling: true,
            pollingInterval: 3000,
            customDevices: [
                // 使用原插件的设备数据
                { id: 'dev-01', name: '放板机', x: 50, y: 50, status: 'running', connections: ['dev-02'], tooltipData: { '型号': 'LDR-2000', '厂商': 'A-Tech', '投入日期': '2022-08-15' } },
                { id: 'dev-02', name: '激光打码', x: 200, y: 50, status: 'running', connections: ['dev-03'], icons: { camera: true }, tooltipData: { '型号': 'LM-500', '功率': '50W', '厂商': 'B-Laser' } },
                { id: 'dev-03', name: '线路前处理', x: 350, y: 50, status: 'running', connections: ['dev-04'], tooltipData: { '处理方式': '等离子清洗', '厂商': 'C-Process' } },
                { id: 'dev-04', name: '暂存机', x: 500, y: 50, status: 'idle', connections: ['dev-05'] },
                { id: 'dev-05', name: '整平机', x: 650, y: 50, status: 'running', connections: ['dev-06'] },
                { id: 'dev-06', name: '涂布机', x: 800, y: 50, status: 'running', connections: ['dev-07'] },
                { id: 'dev-07', name: '隧道炉', x: 950, y: 50, status: 'running', connections: ['dev-08'] },
                { id: 'dev-08', name: '整平机', x: 1100, y: 50, status: 'running', connections: ['dev-09'] },
                { id: 'dev-09', name: '暂存机', x: 1250, y: 50, status: 'idle', connections: ['dev-10'] },
                { id: 'dev-10', name: '线路DI', x: 1400, y: 50, status: 'running', connections: ['dev-11'], icons: { camera: true } },
                { id: 'dev-11', name: 'NG暂存机', x: 1550, y: 50, status: 'idle', connections: ['dev-12'] },
                { id: 'dev-12', name: '显影蚀刻退膜', x: 1700, y: 50, status: 'running', connections: ['dev-13'], icons: { star: true } },
                { id: 'dev-13', name: 'AOI', x: 1850, y: 50, status: 'running', connections: ['dev-14'] },
                { id: 'dev-14', name: '转角机1', x: 1850, y: 180, status: 'running', connections: ['dev-15'] },
                { id: 'dev-15', name: '侧边一体机', x: 1850, y: 310, status: 'running', connections: ['dev-16'] },
                { id: 'dev-16', name: '打靶', x: 1850, y: 440, status: 'running', connections: ['dev-17'] },
                { id: 'dev-17', name: '转角机3', x: 1850, y: 570, status: 'running', connections: ['dev-18'] },
                { id: 'dev-18', name: '阻焊前处理', x: 1700, y: 570, status: 'running', connections: ['dev-19'] },
                { id: 'dev-19', name: '暂存机', x: 1550, y: 570, status: 'idle', connections: ['dev-20'] },
                { id: 'dev-20', name: '阻焊涂布', x: 1400, y: 570, status: 'running', connections: ['dev-21'] },
                { id: 'dev-21', name: '隧道炉', x: 1250, y: 570, status: 'running', connections: ['dev-22'] },
                { id: 'dev-22', name: '先进先出暂存机', x: 1100, y: 570, status: 'idle', connections: ['dev-23'] },
                { id: 'dev-23', name: '暂存机', x: 950, y: 570, status: 'running', connections: ['dev-24'], icons: { camera: true } },
                { id: 'dev-24', name: '阻焊DI', x: 800, y: 570, status: 'idle', connections: ['dev-25'] },
                { id: 'dev-25', name: 'NG暂存机', x: 650, y: 570, status: 'running', connections: ['dev-26'], icons: { star: true } },
                { id: 'dev-26', name: '显影', x: 500, y: 570, status: 'idle', connections: ['dev-27'] },
                { id: 'dev-27', name: '暂存机', x: 500, y: 440, status: 'idle', connections: ['dev-28'] },
                { id: 'dev-28', name: '文字', x: 350, y: 440, status: 'running', connections: ['dev-29'] },
                { id: 'dev-29', name: '隧道炉', x: 200, y: 440, status: 'running', connections: ['dev-30'] },
                { id: 'dev-30', name: '收板机', x: 50, y: 440, status: 'running', connections: [] }
            ]
        };

        // 初始化组件
        function initComponent() {
            componentInstance = new CI.Web.Plugins.Bulletin.equipmentMonitoring({
                container: document.getElementById('container'),
                option: initialOption
            });
        }

        // 更新组件
        function updateComponent() {
            const newOption = {
                ...initialOption,
                backgroundColor: document.getElementById('backgroundColor').value,
                layoutBackgroundColor: document.getElementById('layoutBackgroundColor').value,
                backgroundOpacity: parseFloat(document.getElementById('backgroundOpacity').value),
                headerColor: document.getElementById('headerColor').value,
                showHeader: document.getElementById('showHeader').checked,
                enablePolling: pollingEnabled
            };

            if (componentInstance) {
                componentInstance.updateOption(newOption);
            }
        }

        // 切换轮询
        function togglePolling() {
            pollingEnabled = !pollingEnabled;
            updateComponent();
        }

        // 监听透明度滑块变化
        document.getElementById('backgroundOpacity').addEventListener('input', function(e) {
            document.getElementById('opacityValue').textContent = e.target.value;
        });

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initComponent();
        });
    </script>
</body>
</html>
