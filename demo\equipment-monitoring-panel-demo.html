<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备监控台属性面板演示</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #f0f2f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .demo-layout {
            display: flex;
            height: 100vh;
        }
        .main-content {
            flex: 1;
            background: #1a1a1a;
            position: relative;
            overflow: hidden;
        }
        .properties-panel {
            width: 350px;
            background: white;
            border-left: 1px solid #e4e7ed;
            overflow-y: auto;
            padding: 20px;
        }
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            position: absolute;
            top: 0;
            left: 0;
            right: 350px;
            z-index: 100;
        }
        .demo-header h1 {
            margin: 0;
            font-size: 24px;
        }
        .demo-header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }
        .canvas-area {
            margin-top: 80px;
            height: calc(100vh - 80px);
            position: relative;
        }
        .equipment-container {
            width: 100%;
            height: 100%;
            position: relative;
        }
        .panel-title {
            font-size: 18px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #409eff;
        }
        .instructions {
            background: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .instructions h4 {
            margin: 0 0 10px 0;
            color: #1e40af;
        }
        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }
        .instructions li {
            margin-bottom: 5px;
            color: #374151;
        }
        .status-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 350px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            font-size: 12px;
            z-index: 100;
        }
    </style>
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.6/lib/theme-chalk/index.css">
</head>
<body>
    <div class="demo-layout">
        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="demo-header">
                <h1>🔧 设备监控台属性面板演示</h1>
                <p>展示可展开/折叠的设备属性面板功能</p>
            </div>
            
            <div class="canvas-area">
                <div id="equipment-container" class="equipment-container">
                    <!-- 设备监控台组件将在这里渲染 -->
                </div>
            </div>
            
            <div class="status-bar">
                状态: <span id="status-text">等待初始化...</span> | 
                设备数量: <span id="device-count">0</span> | 
                最后更新: <span id="last-update">--</span>
            </div>
        </div>
        
        <!-- 属性面板 -->
        <div class="properties-panel">
            <div class="panel-title">⚙️ 设备属性面板</div>
            
            <div class="instructions">
                <h4>📋 操作说明</h4>
                <ul>
                    <li>点击"展开设备面板"查看设备管理界面</li>
                    <li>使用"添加设备"按钮新增设备</li>
                    <li>点击设备卡片的"选择"按钮进入快速编辑模式</li>
                    <li>在快速编辑模式下可直接修改坐标和状态</li>
                    <li>支持复制、编辑、删除设备操作</li>
                    <li>可批量编辑或加载示例数据</li>
                </ul>
            </div>
            
            <!-- 配置组件将在这里渲染 -->
            <div id="properties-container"></div>
        </div>
    </div>

    <!-- Vue和Element UI -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.6/lib/index.js"></script>
    
    <script>
        // 模拟设备监控台组件
        Vue.component('equipment-monitoring', {
            props: {
                option: {
                    type: Object,
                    default: () => ({})
                }
            },
            template: `
                <div class="equipment-monitoring-container" :style="containerStyle">
                    <div ref="zoomContainer" class="zoom-container">
                        <div 
                            v-for="device in devices" 
                            :key="device.id"
                            class="device"
                            :class="device.status"
                            :style="getDeviceStyle(device)"
                            @click="showDeviceInfo(device)"
                            @mouseover="showTooltip(device, $event)"
                            @mouseout="hideTooltip"
                        >
                            <div class="device-name">{{ device.name }}</div>
                            <div class="device-status">{{ getStatusText(device.status) }}</div>
                            <div class="device-icons" v-if="device.icons">
                                <i v-if="device.icons.camera" class="el-icon-video-camera"></i>
                                <i v-if="device.icons.star" class="el-icon-star-on"></i>
                                <i v-if="device.status === 'alarm'" class="el-icon-warning"></i>
                            </div>
                        </div>
                        
                        <!-- 连接线 -->
                        <svg class="connections-svg" :style="svgStyle">
                            <path 
                                v-for="connection in connections" 
                                :key="connection.id"
                                :d="connection.path"
                                stroke="#666"
                                stroke-width="2"
                                fill="none"
                            />
                        </svg>
                    </div>
                    
                    <!-- 简化的提示框 -->
                    <div v-if="showTooltipFlag" class="tooltip" :style="tooltipStyle">
                        <div v-for="(value, key) in tooltipData" :key="key">
                            <strong>{{ key }}:</strong> {{ value }}
                        </div>
                    </div>
                </div>
            `,
            data() {
                return {
                    devices: [],
                    connections: [],
                    showTooltipFlag: false,
                    tooltipData: {},
                    tooltipStyle: {}
                }
            },
            computed: {
                containerStyle() {
                    return {
                        width: '100%',
                        height: '100%',
                        backgroundColor: this.option.backgroundColor || '#1a1a1a',
                        color: this.option.textColor || '#ffffff',
                        position: 'relative',
                        overflow: 'hidden'
                    }
                },
                svgStyle() {
                    return {
                        position: 'absolute',
                        top: '0',
                        left: '0',
                        width: '100%',
                        height: '100%',
                        pointerEvents: 'none',
                        zIndex: 1
                    }
                }
            },
            watch: {
                'option.customDevices': {
                    handler(newDevices) {
                        if (newDevices && newDevices.length > 0) {
                            this.devices = [...newDevices]
                            this.updateConnections()
                            this.updateStatus()
                        } else {
                            this.devices = []
                            this.connections = []
                        }
                    },
                    immediate: true,
                    deep: true
                }
            },
            methods: {
                getDeviceStyle(device) {
                    let backgroundColor = '#666'
                    let borderColor = '#555'
                    
                    if (device.status === 'running') {
                        backgroundColor = '#4CAF50'
                        borderColor = '#45a049'
                    } else if (device.status === 'idle') {
                        backgroundColor = '#FFC107'
                        borderColor = '#e0a800'
                    } else if (device.status === 'alarm') {
                        backgroundColor = '#F44336'
                        borderColor = '#da190b'
                    }
                    
                    return {
                        position: 'absolute',
                        left: device.x + 'px',
                        top: device.y + 'px',
                        width: '120px',
                        height: '70px',
                        backgroundColor,
                        borderColor,
                        border: '2px solid',
                        borderRadius: '8px',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        cursor: 'pointer',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        textAlign: 'center',
                        transition: 'all 0.3s ease',
                        color: device.status === 'idle' ? '#000' : '#fff',
                        zIndex: 2
                    }
                },
                getStatusText(status) {
                    const statusMap = {
                        running: '运行中',
                        idle: '空闲',
                        alarm: '报警'
                    }
                    return statusMap[status] || status
                },
                showDeviceInfo(device) {
                    this.$message({
                        message: `设备: ${device.name} | 状态: ${this.getStatusText(device.status)}`,
                        type: device.status === 'alarm' ? 'error' : 'success'
                    })
                },
                showTooltip(device, event) {
                    if (device.tooltipData) {
                        this.tooltipData = device.tooltipData
                        this.tooltipStyle = {
                            position: 'absolute',
                            left: (event.pageX + 15) + 'px',
                            top: (event.pageY + 15) + 'px',
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            color: 'white',
                            padding: '8px 12px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            zIndex: 1000,
                            pointerEvents: 'none'
                        }
                        this.showTooltipFlag = true
                    }
                },
                hideTooltip() {
                    this.showTooltipFlag = false
                },
                updateConnections() {
                    this.connections = []
                    this.devices.forEach(startDevice => {
                        if (startDevice.connections) {
                            startDevice.connections.forEach(endDeviceId => {
                                const endDevice = this.devices.find(d => d.id === endDeviceId)
                                if (endDevice) {
                                    const startX = startDevice.x + 60
                                    const startY = startDevice.y + 35
                                    const endX = endDevice.x + 60
                                    const endY = endDevice.y + 35
                                    
                                    this.connections.push({
                                        id: `${startDevice.id}-${endDevice.id}`,
                                        path: `M ${startX} ${startY} L ${endX} ${endY}`
                                    })
                                }
                            })
                        }
                    })
                },
                updateStatus() {
                    document.getElementById('device-count').textContent = this.devices.length
                    document.getElementById('last-update').textContent = new Date().toLocaleTimeString()
                }
            }
        })
        
        // 模拟配置组件
        Vue.component('equipment-monitoring-option', {
            props: {
                main: {
                    type: Object,
                    required: true
                }
            },
            template: `
                <div class="equipment-monitoring-option">
                    <el-form :model="main.activeOption" label-width="80px" size="small">
                        <!-- 基础配置 -->
                        <el-form-item label="组件标题">
                            <el-input v-model="main.activeOption.title" placeholder="产线设备实时监控"></el-input>
                        </el-form-item>
                        
                        <!-- 设备属性面板 -->
                        <el-divider content-position="left">设备配置</el-divider>
                        
                        <el-form-item>
                            <template slot="label">
                                <span>设备面板</span>
                                <el-tooltip content="点击展开/折叠设备管理面板" placement="top">
                                    <i class="el-icon-question" style="margin-left: 5px;"></i>
                                </el-tooltip>
                            </template>
                            <el-button 
                                type="primary" 
                                size="small" 
                                @click="devicePanelVisible = !devicePanelVisible"
                                :icon="devicePanelVisible ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                            >
                                {{ devicePanelVisible ? '折叠' : '展开' }}设备面板
                            </el-button>
                        </el-form-item>
                        
                        <!-- 可展开的设备管理面板 -->
                        <el-collapse-transition>
                            <div v-show="devicePanelVisible" class="device-management-panel">
                                <el-card shadow="never">
                                    <div slot="header" class="panel-header">
                                        <span>设备管理</span>
                                        <div class="panel-actions">
                                            <el-button type="primary" size="mini" @click="addDevice" icon="el-icon-plus">添加</el-button>
                                            <el-button type="info" size="mini" @click="loadSampleData" icon="el-icon-download">示例</el-button>
                                        </div>
                                    </div>
                                    
                                    <!-- 设备列表 -->
                                    <div class="device-list-container">
                                        <div v-if="!main.activeOption.customDevices || main.activeOption.customDevices.length === 0" class="empty-state">
                                            <i class="el-icon-box" style="font-size: 32px; color: #ddd;"></i>
                                            <p style="color: #999; margin: 10px 0;">暂无设备数据</p>
                                            <el-button type="primary" size="mini" @click="addDevice">添加设备</el-button>
                                        </div>
                                        
                                        <div v-else class="device-list">
                                            <div v-for="(device, index) in main.activeOption.customDevices" :key="device.id || index" class="device-item">
                                                <el-card shadow="hover" :class="{ 'device-selected': selectedDeviceIndex === index }">
                                                    <div slot="header" class="device-header">
                                                        <div class="device-title">
                                                            <span class="device-status-dot" :class="device.status"></span>
                                                            <span class="device-name">{{ device.name }}</span>
                                                            <el-tag size="mini" :type="getStatusTagType(device.status)">{{ getStatusText(device.status) }}</el-tag>
                                                        </div>
                                                        <div class="device-actions">
                                                            <el-button type="text" size="mini" @click="selectDevice(index)">{{ selectedDeviceIndex === index ? '取消' : '选择' }}</el-button>
                                                            <el-button type="text" size="mini" @click="removeDevice(index)" style="color: #f56c6c;">删除</el-button>
                                                        </div>
                                                    </div>
                                                    <div class="device-info">
                                                        <div class="info-row">
                                                            <span class="info-label">位置:</span>
                                                            <span class="info-value">({{ device.x }}, {{ device.y }})</span>
                                                        </div>
                                                    </div>
                                                    
                                                    <!-- 快速编辑区域 -->
                                                    <div v-if="selectedDeviceIndex === index" class="quick-edit-area">
                                                        <el-divider content-position="center">快速编辑</el-divider>
                                                        <el-row :gutter="10">
                                                            <el-col :span="12">
                                                                <el-form-item label="X" label-width="20px">
                                                                    <el-input-number v-model="device.x" size="mini" :min="0" controls-position="right"></el-input-number>
                                                                </el-form-item>
                                                            </el-col>
                                                            <el-col :span="12">
                                                                <el-form-item label="Y" label-width="20px">
                                                                    <el-input-number v-model="device.y" size="mini" :min="0" controls-position="right"></el-input-number>
                                                                </el-form-item>
                                                            </el-col>
                                                        </el-row>
                                                        <el-form-item label="状态" label-width="40px">
                                                            <el-select v-model="device.status" size="mini">
                                                                <el-option label="运行中" value="running"></el-option>
                                                                <el-option label="空闲" value="idle"></el-option>
                                                                <el-option label="报警" value="alarm"></el-option>
                                                            </el-select>
                                                        </el-form-item>
                                                    </div>
                                                </el-card>
                                            </div>
                                        </div>
                                    </div>
                                </el-card>
                            </div>
                        </el-collapse-transition>
                    </el-form>
                </div>
            `,
            data() {
                return {
                    devicePanelVisible: false,
                    selectedDeviceIndex: -1
                }
            },
            methods: {
                addDevice() {
                    const newDevice = {
                        id: \`dev-\${Date.now()}\`,
                        name: \`设备\${(this.main.activeOption.customDevices || []).length + 1}\`,
                        x: 50 + (this.main.activeOption.customDevices || []).length * 150,
                        y: 100,
                        status: 'running',
                        connections: [],
                        icons: {},
                        tooltipData: { '型号': 'DEV-001', '状态': '正常' }
                    }
                    
                    if (!this.main.activeOption.customDevices) {
                        this.$set(this.main.activeOption, 'customDevices', [])
                    }
                    this.main.activeOption.customDevices.push(newDevice)
                    this.$message.success('设备添加成功')
                    this.updateStatus('添加了新设备')
                },
                removeDevice(index) {
                    this.main.activeOption.customDevices.splice(index, 1)
                    this.selectedDeviceIndex = -1
                    this.$message.success('设备删除成功')
                    this.updateStatus('删除了设备')
                },
                selectDevice(index) {
                    this.selectedDeviceIndex = this.selectedDeviceIndex === index ? -1 : index
                },
                loadSampleData() {
                    const sampleDevices = [
                        { id: 'dev-01', name: '放板机', x: 50, y: 100, status: 'running', connections: ['dev-02'], tooltipData: { '型号': 'LDR-2000', '厂商': 'A-Tech' } },
                        { id: 'dev-02', name: '激光打码', x: 200, y: 100, status: 'running', connections: ['dev-03'], icons: { camera: true }, tooltipData: { '型号': 'LM-500', '功率': '50W' } },
                        { id: 'dev-03', name: '前处理', x: 350, y: 100, status: 'idle', connections: ['dev-04'], tooltipData: { '处理方式': '等离子清洗' } },
                        { id: 'dev-04', name: '暂存机', x: 500, y: 100, status: 'alarm', connections: [], tooltipData: { '报警': '温度过高' } }
                    ]
                    this.$set(this.main.activeOption, 'customDevices', sampleDevices)
                    this.$message.success('示例数据加载成功')
                    this.updateStatus('加载了示例数据')
                },
                getStatusText(status) {
                    const statusMap = { running: '运行中', idle: '空闲', alarm: '报警' }
                    return statusMap[status] || status
                },
                getStatusTagType(status) {
                    const typeMap = { running: 'success', idle: 'warning', alarm: 'danger' }
                    return typeMap[status] || 'info'
                },
                updateStatus(message) {
                    document.getElementById('status-text').textContent = message
                }
            }
        })
        
        // 主应用
        let demoApp;
        
        function initDemo() {
            // 设备监控台组件
            new Vue({
                el: '#equipment-container',
                template: '<equipment-monitoring :option="option"></equipment-monitoring>',
                data: {
                    option: {
                        title: "产线设备实时监控",
                        backgroundColor: "#1a1a1a",
                        textColor: "#ffffff",
                        customDevices: []
                    }
                }
            })
            
            // 属性面板组件
            demoApp = new Vue({
                el: '#properties-container',
                template: '<equipment-monitoring-option :main="main"></equipment-monitoring-option>',
                data: {
                    main: {
                        activeOption: {
                            title: "产线设备实时监控",
                            backgroundColor: "#1a1a1a",
                            textColor: "#ffffff",
                            customDevices: []
                        }
                    }
                },
                watch: {
                    'main.activeOption.customDevices': {
                        handler(newDevices) {
                            // 同步到设备监控台组件
                            const equipmentApp = document.getElementById('equipment-container').__vue__
                            if (equipmentApp) {
                                equipmentApp.option.customDevices = newDevices
                            }
                        },
                        deep: true
                    }
                }
            })
            
            document.getElementById('status-text').textContent = '演示环境已初始化'
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initDemo, 500)
        })
    </script>
    
    <style>
        /* 设备管理面板样式 */
        .device-management-panel {
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            background: #fafafa;
            padding: 10px;
        }
        
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .panel-actions {
            display: flex;
            gap: 5px;
        }
        
        .device-list-container {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .empty-state {
            text-align: center;
            padding: 20px;
        }
        
        .device-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .device-selected {
            border: 2px solid #409eff !important;
        }
        
        .device-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0;
        }
        
        .device-title {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
        }
        
        .device-status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .device-status-dot.running { background-color: #67c23a; }
        .device-status-dot.idle { background-color: #e6a23c; }
        .device-status-dot.alarm { background-color: #f56c6c; }
        
        .device-name {
            font-weight: bold;
            color: #303133;
        }
        
        .device-actions {
            display: flex;
            gap: 5px;
        }
        
        .device-info {
            margin-top: 8px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 12px;
        }
        
        .info-label {
            color: #909399;
            font-weight: bold;
        }
        
        .info-value {
            color: #606266;
        }
        
        .quick-edit-area {
            margin-top: 10px;
            padding: 10px;
            background: #f5f7fa;
            border-radius: 4px;
        }
    </style>
</body>
</html>
