# 设备监控台插件 - 属性面板功能更新

## 🆕 新增功能

### 可展开/折叠的设备属性面板

根据您的需求，我已经在设备监控台插件的配置组件中添加了一个可展开/折叠的设备属性面板，让用户可以直接在右侧属性面板中管理设备。

## ✨ 主要特性

### 1. 展开/折叠控制
- **展开按钮**: 点击"展开设备面板"按钮可以显示设备管理界面
- **折叠按钮**: 点击"折叠设备面板"按钮可以隐藏设备管理界面
- **图标指示**: 按钮图标会根据展开/折叠状态自动切换（上箭头/下箭头）

### 2. 设备管理功能
- **添加设备**: 快速添加新设备到监控台
- **删除设备**: 一键删除不需要的设备
- **选择设备**: 点击"选择"按钮进入快速编辑模式
- **加载示例**: 一键加载预设的示例设备数据

### 3. 设备列表展示
- **状态指示**: 每个设备卡片显示状态点（绿色/黄色/红色）
- **设备信息**: 显示设备名称、状态标签、位置坐标
- **图标显示**: 支持摄像头、星标等图标展示
- **连接关系**: 显示设备间的连接信息

### 4. 快速编辑模式
- **坐标调整**: 直接修改设备的X、Y坐标位置
- **状态切换**: 快速切换设备状态（运行中/空闲/报警）
- **实时更新**: 修改后立即在监控台中生效
- **选中高亮**: 选中的设备卡片会有蓝色边框高亮

### 5. 空状态处理
- **友好提示**: 当没有设备时显示空状态图标和提示文字
- **快速开始**: 提供"添加第一个设备"按钮引导用户

## 🎯 使用方法

### 1. 基本操作流程
1. 在大屏设计器中选中"产线设备实时监控"图层
2. 在右侧属性面板中找到"设备配置"部分
3. 点击"展开设备面板"按钮
4. 使用"添加设备"或"加载示例"开始配置设备
5. 点击设备卡片的"选择"按钮进入快速编辑模式
6. 在快速编辑区域调整设备位置和状态

### 2. 设备添加
```javascript
// 点击"添加设备"会自动创建如下结构的设备
{
  id: "dev-timestamp",
  name: "设备N",
  x: 50 + N * 150,  // 自动计算位置避免重叠
  y: 100,
  status: "running",
  connections: [],
  icons: {},
  tooltipData: {
    "型号": "DEV-001",
    "状态": "正常"
  }
}
```

### 3. 快速编辑
- **位置调整**: 使用数字输入框精确设置X、Y坐标
- **状态切换**: 使用下拉选择框切换设备状态
- **实时预览**: 修改后立即在左侧监控台中看到效果

## 🎨 界面设计

### 视觉特点
- **层次清晰**: 使用卡片布局区分不同设备
- **状态直观**: 彩色状态点和标签快速识别设备状态
- **操作便捷**: 按钮布局合理，操作流程顺畅
- **响应式**: 支持面板滚动，适应不同数量的设备

### 交互体验
- **平滑动画**: 展开/折叠使用过渡动画效果
- **选中反馈**: 选中设备有明显的视觉反馈
- **操作确认**: 删除等操作有消息提示确认
- **状态同步**: 配置面板与监控台实时同步

## 📁 更新的文件

### 主要修改
- `src/components/equipmentMonitoring/option.vue` - 配置组件主文件
  - 新增可展开/折叠的设备管理面板
  - 添加设备列表展示和快速编辑功能
  - 增强设备操作（添加、删除、选择、复制）
  - 优化界面布局和交互体验

### 新增演示
- `demo/equipment-monitoring-panel-demo.html` - 属性面板功能演示页面
  - 完整展示新的属性面板功能
  - 模拟真实的大屏设计器环境
  - 提供交互式操作演示

## 🔧 技术实现

### 核心技术
- **Vue.js 2.x**: 响应式数据绑定和组件化开发
- **Element UI**: 丰富的UI组件库支持
- **CSS Transitions**: 平滑的展开/折叠动画效果
- **Flexbox Layout**: 灵活的布局系统

### 数据流
1. 用户在属性面板中操作设备
2. 数据通过Vue的响应式系统更新
3. 监控台组件监听数据变化
4. 自动重新渲染设备布局

## 🚀 使用建议

### 最佳实践
1. **设备命名**: 使用有意义的设备名称，便于识别
2. **位置规划**: 合理安排设备位置，避免重叠
3. **状态管理**: 及时更新设备状态，保持数据准确性
4. **连接配置**: 正确设置设备间的连接关系

### 操作技巧
1. **批量操作**: 先加载示例数据，再根据需要调整
2. **快速定位**: 使用选择功能快速定位和编辑特定设备
3. **状态监控**: 利用状态点和标签快速识别设备状态
4. **空间利用**: 合理使用面板滚动功能管理大量设备

## 📞 技术支持

如果在使用过程中遇到问题或有功能建议，请随时联系开发团队。

---

**更新版本**: v1.1.0  
**更新时间**: 2024-01-17  
**主要改进**: 新增可展开/折叠的设备属性面板功能  
**兼容性**: 向下兼容，不影响现有功能
