### **项目文档：产线设备实时监控大屏**

---

### **1. 项目概述**

本项目旨在创建一个现代化、信息丰富的生产线设备实时监控大屏。它通过一个动态、可视化的设备布局图，直观地展示产线上每个设备的位置、状态、连接关系和关键信息。该系统具有高度的交互性，支持自适应屏幕缩放，确保在任何大屏设备上都能获得最佳的视觉体验。

### **2. 核心功能**

*   **动态设备布局**：根据预设的坐标和连接数据，自动生成完整的产线设备流程图。
*   **实时状态显示**：设备卡片通过不同的颜色和动画效果，清晰地展示三种工作状态：
    *   **运行 (Running)**: 绿色边框
    *   **闲置 (Idle)**: 蓝色边框
    *   **报警 (Alarm)**: 红色闪烁边框及告警图标
*   **物料流动动画**：在设备连接线上使用流动光点动画，模拟物料在产线上的流转方向。
*   **自适应屏幕缩放**：无论在哪种分辨率的大屏上显示，整体布局都会自动缩放以适应屏幕，避免出现滚动条，保证信息的完整呈现。
*   **交互式设备详情**：
    *   点击任意设备，会弹出一个包含详细信息的模态窗口。
    *   弹窗内支持标签页切换，可查看 **关键参数**、**保养信息** 和 **报警详情**。
    *   当点击处于报警状态的设备时，弹窗会自动切换到“报警详情”标签页。
*   **摄像头监控**：带有摄像头图标的设备，点击后可以弹出一个模拟的实时视频监控窗口。

### **3. 文件结构**

项目由三个核心文件组成，完全基于前端技术，无需后端依赖。

*   `index.html`: **页面骨架**
    *   定义了页面的基本结构，包括标题、主容器、设备布局容器 (`device-layout`) 以及用于缩放的 `zoom-container`。
    *   包含了两个预设的模态窗口（设备详情和摄像头监控）的 HTML 结构。
    *   引入了 `style.css` 样式文件、`script.js` 脚本文件以及 `FontAwesome` 图标库。

*   `style.css`: **视觉样式**
    *   定义了整体的深色科技感主题，包括背景、字体颜色等。
    *   实现了设备卡片在不同状态下的样式（边框颜色、报警动画）。
    *   定义了连接线（SVG path）和流动光点动画的样式。
    *   包含了模态弹窗和内部标签页的全部样式。
    *   通过 `overflow: hidden` 和 `flex` 布局确保缩放容器能完美居中。

*   `script.js`: **核心逻辑**
    *   包含了项目的全部交互逻辑和动态渲染功能。
    *   是进行自定义和维护的核心文件。

### **4. 技术实现详解**

#### **4.1 数据驱动的布局**

项目的核心是 `script.js` 中的 `devices`常量数组。每个设备都是一个对象，其结构如下：

```javascript
{
    id: 'dev-01',           // 唯一ID，用于DOM操作
    name: '放板机',          // 设备名称
    x: 50,                  // X轴坐标 (px)
    y: 50,                  // Y轴坐标 (px)
    status: 'running',      // 初始状态
    connections: ['dev-02'],// 连接到下一个设备的ID数组
    icons: { camera: true } // 可选，定义特殊图标
}
```

*   **渲染**：页面加载时，脚本会遍历此数组，在 `#zoom-container` 内动态创建每个设备的 `<div>` 元素，并根据其 `x` 和 `y` 坐标进行绝对定位。
*   **连接**：脚本会再次遍历数组，根据 `connections` 属性，使用 SVG `path` 元素在设备之间绘制连接线。

#### **4.2 连接线绘制算法**

为了能正确绘制直线和转角线，`script.js` 中实现了一个智能路径绘制算法：
1.  获取起始和结束两个设备的中心点坐标。
2.  判断两个设备是水平对齐、垂直对齐还是需要转角。
3.  如果对齐，则生成一条简单的直线路径 (`M x1 y1 L x2 y2`)。
4.  如果需要转角，则生成一条 "L" 形路径 (`M x1 y1 L x1 y2 L x2 y2`)，该路径优先进行垂直移动，再进行水平移动，使布局更规整。

#### **4.3 自适应缩放 (`updateZoom` 函数)**

这是实现大屏无缝显示的关键。
1.  **计算内容尺寸**：遍历所有设备，找出布局内容所需的最大宽度和高度。
2.  **计算容器尺寸**：获取父容器 `#device-layout` 的可用宽度和高度。
3.  **计算缩放比例**：分别计算宽度和高度的缩放比（`容器尺寸 / 内容尺寸`），并取其中较小的一个值，以确保内容能完整显示。同时，最大缩放比例不超过 `1`，避免在超大屏幕上过度放大。
4.  **应用缩放**：通过 CSS `transform: scale(ratio)` 将计算出的缩放比例应用到 `#zoom-container` 上。
5.  **动态响应**：通过监听 `window` 的 `resize` 事件，在浏览器窗口大小变化时重新执行以上逻辑，实现动态自适应。

#### **4.4 交互逻辑**

*   所有点击事件都委托给了 `#zoom-container`，以提升性能。
*   通过 `event.target.closest('.device')` 来判断点击是否发生在设备上。
*   根据点击的是否为摄像头图标 (`.camera-btn`)，来决定打开哪个模态窗口。
*   弹窗内的标签页切换通过简单的 `active` 类的添加和移除实现。

### **5. 如何使用与自定义**

#### **运行项目**

直接在浏览器中打开 `index.html` 文件即可。为了获得最佳体验（特别是对于流动动画），建议通过一个简单的本地服务器（如 VS Code 的 Live Server 插件）来运行。

#### **自定义布局**

要修改设备布局、名称、位置或连接关系，**只需修改 `script.js` 文件中的 `devices` 数组即可**。

*   **添加设备**：在数组中增加一个新的设备对象，并定义好 `id`, `name`, `x`, `y`。
*   **修改连接**：找到起始设备，在其 `connections` 数组中添加或移除目标设备的 `id`。
*   **调整位置**：直接修改设备的 `x` 和 `y` 坐标值。

所有更改都会在刷新页面后自动生效，无需修改任何 HTML 或 CSS 代码。

---

这份文档应该涵盖了项目的所有关键信息。如果您需要任何部分的进一步说明，请随时告诉我。
