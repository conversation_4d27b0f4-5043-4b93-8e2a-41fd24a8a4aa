<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备监控台插件测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .test-header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-content {
            padding: 20px;
        }
        .equipment-container {
            height: 700px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
            background: #1a1a1a;
        }
        .controls {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 设备监控台插件测试</h1>
            <p>验证设备监控台插件的基本功能</p>
        </div>
        
        <div class="test-content">
            <div class="controls">
                <h3>测试控制</h3>
                <button class="btn" onclick="loadBasicTest()">基础测试</button>
                <button class="btn" onclick="loadComplexTest()">复杂布局测试</button>
                <button class="btn" onclick="loadAlarmTest()">报警状态测试</button>
                <button class="btn" onclick="clearTest()">清空测试</button>
                <button class="btn" onclick="randomizeStatus()">随机状态</button>
                
                <div class="status" id="status">
                    状态: 等待测试...
                </div>
            </div>
            
            <div id="equipment-test" class="equipment-container">
                <!-- 设备监控台组件将在这里渲染 -->
            </div>
        </div>
    </div>

    <!-- 引入Vue和Element UI -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.6/lib/index.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.6/lib/theme-chalk/index.css">
    
    <script>
        // 模拟设备监控台组件
        Vue.component('equipment-monitoring', {
            props: {
                option: {
                    type: Object,
                    default: () => ({})
                }
            },
            template: `
                <div class="equipment-monitoring-container" :style="containerStyle">
                    <header v-if="option.showHeader">
                        <h1 :style="headerStyle">{{ option.title || '产线设备实时监控' }}</h1>
                    </header>
                    <main class="device-layout" ref="deviceLayout">
                        <div ref="zoomContainer" class="zoom-container">
                            <div 
                                v-for="device in devices" 
                                :key="device.id"
                                class="device"
                                :class="device.status"
                                :style="getDeviceStyle(device)"
                                @click="showDeviceInfo(device)"
                                @mouseover="showTooltip(device, $event)"
                                @mouseout="hideTooltip"
                            >
                                <div class="device-name">{{ device.name }}</div>
                                <div class="device-status">{{ getStatusText(device.status) }}</div>
                                <div class="device-icons" v-if="device.icons">
                                    <i v-if="device.icons.camera" class="el-icon-video-camera"></i>
                                    <i v-if="device.icons.star" class="el-icon-star-on"></i>
                                    <i v-if="device.status === 'alarm'" class="el-icon-warning"></i>
                                </div>
                            </div>
                        </div>
                    </main>
                    
                    <!-- 简化的提示框 -->
                    <div v-if="showTooltipFlag" class="tooltip" :style="tooltipStyle">
                        <div v-for="(value, key) in tooltipData" :key="key">
                            <strong>{{ key }}:</strong> {{ value }}
                        </div>
                    </div>
                </div>
            `,
            data() {
                return {
                    devices: [],
                    showTooltipFlag: false,
                    tooltipData: {},
                    tooltipStyle: {}
                }
            },
            computed: {
                containerStyle() {
                    return {
                        width: '100%',
                        height: '100%',
                        backgroundColor: this.option.backgroundColor || '#1a1a1a',
                        color: this.option.textColor || '#ffffff',
                        position: 'relative',
                        overflow: 'hidden'
                    }
                },
                headerStyle() {
                    return {
                        fontSize: (this.option.headerFontSize || 24) + 'px',
                        color: this.option.headerColor || '#ffffff',
                        textAlign: this.option.headerAlign || 'center',
                        margin: '0',
                        padding: '20px'
                    }
                }
            },
            watch: {
                'option.customDevices': {
                    handler(newDevices) {
                        if (newDevices && newDevices.length > 0) {
                            this.devices = [...newDevices]
                        }
                    },
                    immediate: true,
                    deep: true
                }
            },
            methods: {
                getDeviceStyle(device) {
                    let backgroundColor = '#666'
                    let borderColor = '#555'
                    
                    if (device.status === 'running') {
                        backgroundColor = '#4CAF50'
                        borderColor = '#45a049'
                    } else if (device.status === 'idle') {
                        backgroundColor = '#FFC107'
                        borderColor = '#e0a800'
                    } else if (device.status === 'alarm') {
                        backgroundColor = '#F44336'
                        borderColor = '#da190b'
                    }
                    
                    return {
                        position: 'absolute',
                        left: device.x + 'px',
                        top: device.y + 'px',
                        width: '120px',
                        height: '70px',
                        backgroundColor,
                        borderColor,
                        border: '2px solid',
                        borderRadius: '8px',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        cursor: 'pointer',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        textAlign: 'center',
                        transition: 'all 0.3s ease',
                        color: device.status === 'idle' ? '#000' : '#fff'
                    }
                },
                getStatusText(status) {
                    const statusMap = {
                        running: '运行中',
                        idle: '空闲',
                        alarm: '报警'
                    }
                    return statusMap[status] || status
                },
                showDeviceInfo(device) {
                    this.$message({
                        message: `设备: ${device.name} | 状态: ${this.getStatusText(device.status)}`,
                        type: device.status === 'alarm' ? 'error' : 'success'
                    })
                },
                showTooltip(device, event) {
                    if (device.tooltipData) {
                        this.tooltipData = device.tooltipData
                        this.tooltipStyle = {
                            position: 'absolute',
                            left: (event.pageX + 15) + 'px',
                            top: (event.pageY + 15) + 'px',
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            color: 'white',
                            padding: '8px 12px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            zIndex: 1000,
                            pointerEvents: 'none'
                        }
                        this.showTooltipFlag = true
                    }
                },
                hideTooltip() {
                    this.showTooltipFlag = false
                }
            }
        })
        
        // 测试应用
        let testApp;
        
        function initTest() {
            testApp = new Vue({
                el: '#equipment-test',
                template: '<equipment-monitoring :option="option"></equipment-monitoring>',
                data: {
                    option: {
                        title: "设备监控台测试",
                        showHeader: true,
                        backgroundColor: "#1a1a1a",
                        textColor: "#ffffff",
                        headerFontSize: 24,
                        headerColor: "#ffffff",
                        headerAlign: "center",
                        customDevices: []
                    }
                }
            })
            updateStatus('测试环境已初始化')
        }
        
        function loadBasicTest() {
            const basicDevices = [
                { id: 'dev-01', name: '设备A', x: 50, y: 100, status: 'running', tooltipData: { '型号': 'A-001', '状态': '正常' } },
                { id: 'dev-02', name: '设备B', x: 250, y: 100, status: 'idle', tooltipData: { '型号': 'B-002', '状态': '待机' } },
                { id: 'dev-03', name: '设备C', x: 450, y: 100, status: 'alarm', tooltipData: { '型号': 'C-003', '状态': '故障' } }
            ]
            testApp.option.customDevices = basicDevices
            updateStatus('基础测试已加载 - 3个设备，不同状态')
        }
        
        function loadComplexTest() {
            const complexDevices = [
                { id: 'dev-01', name: '放板机', x: 50, y: 50, status: 'running', connections: ['dev-02'], tooltipData: { '型号': 'LDR-2000', '厂商': 'A-Tech' } },
                { id: 'dev-02', name: '激光打码', x: 200, y: 50, status: 'running', connections: ['dev-03'], icons: { camera: true }, tooltipData: { '型号': 'LM-500', '功率': '50W' } },
                { id: 'dev-03', name: '前处理', x: 350, y: 50, status: 'running', connections: ['dev-04'], tooltipData: { '处理方式': '等离子清洗' } },
                { id: 'dev-04', name: '暂存机', x: 500, y: 50, status: 'idle', connections: ['dev-05'] },
                { id: 'dev-05', name: '整平机', x: 650, y: 50, status: 'running', connections: ['dev-06'] },
                { id: 'dev-06', name: '涂布机', x: 800, y: 50, status: 'running', connections: ['dev-07'] },
                { id: 'dev-07', name: '隧道炉', x: 950, y: 50, status: 'running', connections: ['dev-08'] },
                { id: 'dev-08', name: '收板机', x: 1100, y: 50, status: 'running', connections: [] }
            ]
            testApp.option.customDevices = complexDevices
            updateStatus('复杂布局测试已加载 - 8个设备，生产线布局')
        }
        
        function loadAlarmTest() {
            const alarmDevices = [
                { id: 'dev-01', name: '设备1', x: 100, y: 100, status: 'alarm', tooltipData: { '报警': '温度过高', '时间': '14:30' } },
                { id: 'dev-02', name: '设备2', x: 300, y: 100, status: 'alarm', tooltipData: { '报警': '压力异常', '时间': '14:25' } },
                { id: 'dev-03', name: '设备3', x: 500, y: 100, status: 'running', tooltipData: { '状态': '正常运行' } },
                { id: 'dev-04', name: '设备4', x: 700, y: 100, status: 'alarm', tooltipData: { '报警': '通讯中断', '时间': '14:20' } }
            ]
            testApp.option.customDevices = alarmDevices
            updateStatus('报警状态测试已加载 - 3个报警设备，1个正常设备')
        }
        
        function clearTest() {
            testApp.option.customDevices = []
            updateStatus('测试已清空')
        }
        
        function randomizeStatus() {
            if (testApp.option.customDevices.length === 0) {
                updateStatus('错误: 请先加载测试数据')
                return
            }
            
            testApp.option.customDevices.forEach(device => {
                const rand = Math.random()
                if (rand < 0.2) { device.status = 'alarm' }
                else if (rand < 0.5) { device.status = 'idle' }
                else { device.status = 'running' }
            })
            
            testApp.$forceUpdate()
            updateStatus('设备状态已随机化')
        }
        
        function updateStatus(message) {
            document.getElementById('status').textContent = '状态: ' + message + ' (' + new Date().toLocaleTimeString() + ')'
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initTest, 500)
        })
    </script>
</body>
</html>
