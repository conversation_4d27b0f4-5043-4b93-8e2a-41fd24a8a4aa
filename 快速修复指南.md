# 🚨 网页崩溃问题快速修复指南

## 🎯 问题概述

CI.Web.Plugins.Bulletin 项目在运行一段时间后出现网页崩溃，主要原因是内存泄漏和性能问题。

## ⚡ 紧急修复（立即执行）

### 1. 修复表格滚动内存泄漏

**文件**: `src/echart/packages/table/index.vue`

在组件中添加销毁逻辑：

```javascript
// 在 export default create({ 的 methods 后面添加
beforeDestroy() {
  // 清理滚动定时器
  if (this.scrollCheck) {
    clearInterval(this.scrollCheck);
    this.scrollCheck = null;
  }
  
  // 清理其他定时器
  if (this.headerHeightTimer) {
    clearTimeout(this.headerHeightTimer);
    this.headerHeightTimer = null;
  }
  
  console.log('表格组件已清理');
},
```

### 2. 修复Vue组件渲染器内存泄漏

**文件**: `src/echart/packages/vue/index.vue`

在组件中添加清理逻辑：

```javascript
// 在 methods 中添加
beforeDestroy() {
  // 清理动态样式
  const styleId = 'style-' + this.id;
  const existingStyle = document.getElementById(styleId);
  if (existingStyle) {
    existingStyle.remove();
  }
  
  // 清理动态注册的组件
  if (Vue.options.components && Vue.options.components[this.id]) {
    delete Vue.options.components[this.id];
  }
  
  console.log('Vue组件渲染器已清理');
},

// 修改 initVue 方法，在开头添加清理逻辑
initVue() {
  this.reload = false;
  
  // 清理旧的样式和组件
  const styleId = 'style-' + this.id;
  const existingStyle = document.getElementById(styleId);
  if (existingStyle) {
    existingStyle.remove();
  }
  
  // 其余原有代码保持不变...
}
```

### 3. 修复WebSocket连接泄漏

**文件**: `src/echart/common.js`

找到 `closeClient()` 方法（第1145行），替换为：

```javascript
closeClient() {
  if (this.wsClient) {
    if (this.wsClient.readyState === WebSocket.OPEN || 
        this.wsClient.readyState === WebSocket.CONNECTING) {
      this.wsClient.close();
    }
    this.wsClient = null;
  }
},
```

在 `beforeDestroy()` 方法中（第1214行），确保包含：

```javascript
beforeDestroy() {
  // 清理所有定时器
  clearInterval(this.checkChart);
  clearInterval(this.globalTimerCheck);
  clearInterval(this.appendCheck);
  clearTimeout(this.timer_echartFormatter);
  
  // 关闭WebSocket连接
  this.closeClient();
  
  // 清理ECharts实例
  if (this.myChart) {
    this.myChart.dispose();
    this.myChart = null;
  }
  
  console.log('组件公共资源已清理');
},
```

## 🔧 性能优化（推荐执行）

### 1. 降低表格滚动频率

**文件**: `src/echart/packages/table/index.vue`

找到 `setTime()` 方法中的 `setInterval`（第550行），将间隔从20ms改为50ms：

```javascript
this.scrollCheck = setInterval(() => {
  // 滚动逻辑保持不变
}, 50); // 从20改为50，降低CPU占用
```

### 2. 添加内存监控

在项目中添加简单的内存监控，在 `src/main.js` 中添加：

```javascript
// 在文件末尾添加
if (process.env.NODE_ENV === 'development') {
  setInterval(() => {
    if (performance.memory) {
      const memory = performance.memory;
      const used = (memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
      const total = (memory.totalJSHeapSize / 1024 / 1024).toFixed(2);
      console.log(`内存使用: ${used}MB / ${total}MB`);
      
      if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.8) {
        console.warn('⚠️ 内存使用率过高，可能存在内存泄漏');
      }
    }
  }, 10000); // 每10秒检查一次
}
```

## 🧪 验证修复效果

### 1. 浏览器测试

打开浏览器开发者工具，在控制台执行：

```javascript
// 监控内存使用
function monitorMemory() {
  const start = performance.memory.usedJSHeapSize;
  console.log('开始内存:', (start / 1024 / 1024).toFixed(2) + 'MB');
  
  setTimeout(() => {
    const end = performance.memory.usedJSHeapSize;
    const diff = end - start;
    console.log('结束内存:', (end / 1024 / 1024).toFixed(2) + 'MB');
    console.log('内存变化:', (diff / 1024 / 1024).toFixed(2) + 'MB');
    
    if (diff > 50 * 1024 * 1024) {
      console.error('⚠️ 可能存在内存泄漏');
    } else {
      console.log('✅ 内存使用正常');
    }
  }, 60000); // 1分钟后检查
}

monitorMemory();
```

### 2. 压力测试

1. 打开多个包含表格的页面
2. 让页面运行30分钟以上
3. 观察内存使用是否持续增长
4. 检查是否出现页面卡顿或崩溃

## 📋 修复检查清单

完成修复后，请检查以下项目：

- [ ] 表格组件添加了 `beforeDestroy` 方法
- [ ] Vue组件渲染器添加了清理逻辑
- [ ] WebSocket连接管理得到改进
- [ ] 公共组件的 `beforeDestroy` 方法完善
- [ ] 表格滚动频率已降低
- [ ] 添加了内存监控代码

## 🚀 构建和部署

修复完成后：

1. 重新构建项目：
```bash
npm run build
```

2. 测试dist文件：
```bash
# 使用demo目录下的测试文件验证
# 打开 demo/table-dist-example.html 测试
```

3. 部署到生产环境前，建议先在测试环境运行24小时以上

## 📞 紧急联系

如果修复后仍有问题，请：

1. 收集浏览器控制台错误信息
2. 记录内存使用情况
3. 提供复现步骤
4. 联系开发团队进行进一步分析

## ⚠️ 注意事项

1. 修复前请备份原始代码
2. 在测试环境充分验证后再部署生产环境
3. 监控生产环境的内存使用情况
4. 定期检查是否有新的内存泄漏问题

通过以上快速修复，可以立即缓解网页崩溃问题，提升系统稳定性。
