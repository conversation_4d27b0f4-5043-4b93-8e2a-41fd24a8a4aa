<template>
  <div class="equipment-monitoring-option">
    <el-form :model="main.activeOption" label-width="120px">
      <!-- 基础配置 -->
      <el-form-item label="组件标题">
        <el-input v-model="main.activeOption.title" placeholder="产线设备实时监控"></el-input>
      </el-form-item>
      
      <el-form-item label="显示标题">
        <el-switch v-model="main.activeOption.showHeader"></el-switch>
      </el-form-item>
      
      <!-- 样式配置 -->
      <el-divider content-position="left">样式配置</el-divider>
      
      <el-form-item label="背景颜色">
        <el-color-picker v-model="main.activeOption.backgroundColor" show-alpha></el-color-picker>
      </el-form-item>

      <el-form-item label="文字颜色">
        <el-color-picker v-model="main.activeOption.textColor" show-alpha></el-color-picker>
      </el-form-item>
      
      <el-form-item label="标题字体大小">
        <el-input-number v-model="main.activeOption.headerFontSize" :min="12" :max="48" controls-position="right"></el-input-number>
        <span style="margin-left: 10px;">px</span>
      </el-form-item>
      
      <el-form-item label="标题颜色">
        <el-color-picker v-model="main.activeOption.headerColor" show-alpha></el-color-picker>
      </el-form-item>
      
      <el-form-item label="标题对齐">
        <el-select v-model="main.activeOption.headerAlign">
          <el-option label="左对齐" value="left"></el-option>
          <el-option label="居中" value="center"></el-option>
          <el-option label="右对齐" value="right"></el-option>
        </el-select>
      </el-form-item>
      
      <!-- 数据配置 -->
      <el-divider content-position="left">数据配置</el-divider>
      
      <el-form-item label="启用轮询">
        <el-switch v-model="main.activeOption.enablePolling"></el-switch>
      </el-form-item>
      
      <el-form-item label="轮询间隔" v-if="main.activeOption.enablePolling">
        <el-input-number v-model="main.activeOption.pollingInterval" :min="1000" :max="60000" :step="1000" controls-position="right"></el-input-number>
        <span style="margin-left: 10px;">毫秒</span>
      </el-form-item>
      
      <!-- 设备配置 -->
      <el-divider content-position="left">设备配置</el-divider>

      <!-- 设备属性面板 - 可展开折叠 -->
      <el-form-item>
        <template slot="label">
          <span>设备属性</span>
          <el-tooltip content="点击展开/折叠设备管理面板，可直接在此添加、编辑、删除设备" placement="top">
            <i class="el-icon-question" style="margin-left: 5px;"></i>
          </el-tooltip>
        </template>
        <el-button
          type="primary"
          size="small"
          @click="devicePanelVisible = !devicePanelVisible"
          :icon="devicePanelVisible ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
        >
          {{ devicePanelVisible ? '折叠' : '展开' }}设备面板
        </el-button>
      </el-form-item>

      <!-- 可展开的设备管理面板 -->
      <el-collapse-transition>
        <div v-show="devicePanelVisible" class="device-management-panel">
          <el-card shadow="never" style="margin-bottom: 15px;">
            <div slot="header" class="panel-header">
              <div class="panel-actions">
                <el-link type="primary" size="mini" @click="addDevice" icon="el-icon-plus">添加设备</el-link>
                <el-link type="success" size="mini" @click="openDeviceEditor" icon="el-icon-edit">批量编辑</el-link>
                <el-link type="info" size="mini" @click="loadDefaultData" icon="el-icon-download">加载示例</el-link>


              </div>
            </div>

            <!-- 设备列表 -->
            <div class="device-list-container">
              <div v-if="!main.activeOption.customDevices || main.activeOption.customDevices.length === 0" class="empty-state">
                <i class="el-icon-box" style="font-size: 48px; color: #ddd;"></i>
                <p style="color: #999; margin: 10px 0;">暂无设备数据</p>
                <el-button type="primary" size="small" @click="addDevice">添加第一个设备</el-button>
              </div>

              <div v-else class="device-list">
                <div v-for="(device, index) in main.activeOption.customDevices" :key="device.id || index" class="device-item">
                  <el-card shadow="hover" :class="{ 'device-selected': selectedDeviceIndex === index }">
                    <div slot="header" class="device-header">
                      <div class="device-title">
                        <span class="device-status-dot" :class="device.status"></span>
                        <span class="device-name">{{ device.name  }}</span>
                      </div>
                    </div>
                    <div class="device-actions">
                        <el-button type="text" size="mini" @click="editDevice(index)" >编辑</el-button>
                        <el-button type="text" size="mini" @click="duplicateDevice(index)" >复制</el-button>
                        <el-button type="text" size="mini" @click="removeDevice(index)" style="color: #f56c6c;" >删除</el-button>
                      </div>
                  
                  </el-card>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </el-collapse-transition>
    </el-form>
    
    <!-- 设备编辑对话框 -->
    <el-dialog title="设备配置" :visible.sync="deviceDialogVisible" width="600px">
      <el-form :model="currentDevice" label-width="100px">
        <el-form-item label="设备ID" required>
          <el-input v-model="currentDevice.id" placeholder="设备唯一标识"></el-input>
        </el-form-item>
        
        <el-form-item label="设备名称" required>
          <el-input v-model="currentDevice.name" placeholder="设备显示名称"></el-input>
        </el-form-item>
        
        <el-form-item label="X坐标">
          <el-input-number v-model="currentDevice.x" :min="0" controls-position="right"></el-input-number>
        </el-form-item>
        
        <el-form-item label="Y坐标">
          <el-input-number v-model="currentDevice.y" :min="0" controls-position="right"></el-input-number>
        </el-form-item>
        
        <el-form-item label="设备状态">
          <el-select v-model="currentDevice.status">
            <el-option label="运行中" value="running"></el-option>
            <el-option label="空闲" value="idle"></el-option>
            <el-option label="报警" value="alarm"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="连接设备">
          <el-select v-model="currentDevice.connections" multiple placeholder="选择连接的设备">
            <el-option 
              v-for="device in availableDevices" 
              :key="device.id" 
              :label="device.name" 
              :value="device.id"
            ></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="图标配置">
          <el-checkbox v-model="currentDevice.icons.camera">摄像头</el-checkbox>
          <el-checkbox v-model="currentDevice.icons.star">星标</el-checkbox>
        </el-form-item>
        
        <el-form-item label="提示信息">
          <el-button type="text" @click="openTooltipEditor">编辑提示信息</el-button>
        </el-form-item>
      </el-form>
      
      <div slot="footer">
        <el-button @click="deviceDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveDevice">保存</el-button>
      </div>
    </el-dialog>
    
    <!-- 批量编辑对话框 -->
    <el-dialog title="批量编辑设备" :visible.sync="batchEditVisible" width="80%">
      <div style="margin-bottom: 10px;">
        <el-button type="primary" @click="importFromJson">从JSON导入</el-button>
        <el-button type="success" @click="exportToJson">导出为JSON</el-button>
        <el-button type="info" @click="loadDefaultData">加载默认数据</el-button>
      </div>
      <el-input
        type="textarea"
        v-model="deviceJsonData"
        :rows="20"
        placeholder="请输入设备JSON数据..."
      ></el-input>
      <div slot="footer">
        <el-button @click="batchEditVisible = false">取消</el-button>
        <el-button type="primary" @click="saveBatchEdit">保存</el-button>
      </div>
    </el-dialog>
    
    <!-- 提示信息编辑对话框 -->
    <el-dialog title="编辑提示信息" :visible.sync="tooltipEditVisible" width="500px">
      <div v-for="(value, key, index) in currentDevice.tooltipData" :key="index" class="tooltip-item">
        <el-input v-model="tooltipKeys[index]" placeholder="属性名" style="width: 40%; margin-right: 10px;"></el-input>
        <el-input v-model="tooltipValues[index]" placeholder="属性值" style="width: 40%; margin-right: 10px;"></el-input>
        <el-button type="text" @click="removeTooltipItem(index)" style="color: #f56c6c;">删除</el-button>
      </div>
      <el-button type="text" @click="addTooltipItem">+ 添加属性</el-button>
      
      <div slot="footer">
        <el-button @click="tooltipEditVisible = false">取消</el-button>
        <el-button type="primary" @click="saveTooltipData">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'equipmentMonitoring',
  inject: ["main"],
  data() {
    return {
      deviceDialogVisible: false,
      batchEditVisible: false,
      tooltipEditVisible: false,
      devicePanelVisible: false,
      selectedDeviceIndex: -1,
      currentDevice: {
        id: '',
        name: '',
        x: 0,
        y: 0,
        status: 'running',
        connections: [],
        icons: {
          camera: false,
          star: false
        },
        tooltipData: {}
      },
      currentDeviceIndex: -1,
      deviceJsonData: '',
      tooltipKeys: [],
      tooltipValues: []
    }
  },
  computed: {
    availableDevices() {
      if (!this.main.activeOption.customDevices) return []
      return this.main.activeOption.customDevices.filter(device => device.id !== this.currentDevice.id)
    }
  },
  watch: {
    'main.activeOption': {
      handler(newVal) {
        // 初始化默认值
        if (!newVal.customDevices) {
          this.$set(newVal, 'customDevices', [])
        }
        if (newVal.showHeader === undefined) {
          this.$set(newVal, 'showHeader', true)
        }
        if (!newVal.backgroundColor) {
          this.$set(newVal, 'backgroundColor', '#1a1a1a')
        }
        if (!newVal.textColor) {
          this.$set(newVal, 'textColor', '#ffffff')
        }
        if (!newVal.headerFontSize) {
          this.$set(newVal, 'headerFontSize', 24)
        }
        if (!newVal.headerColor) {
          this.$set(newVal, 'headerColor', '#ffffff')
        }
        if (!newVal.headerAlign) {
          this.$set(newVal, 'headerAlign', 'center')
        }
        if (newVal.enablePolling === undefined) {
          this.$set(newVal, 'enablePolling', true)
        }
        if (!newVal.pollingInterval) {
          this.$set(newVal, 'pollingInterval', 3000)
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    addDevice() {
      this.currentDevice = {
        id: `dev-${Date.now()}`,
        name: '',
        x: 50,
        y: 50,
        status: 'running',
        connections: [],
        icons: {
          camera: false,
          star: false
        },
        tooltipData: {}
      }
      this.currentDeviceIndex = -1
      this.deviceDialogVisible = true
    },
    editDevice(index) {
      this.currentDevice = JSON.parse(JSON.stringify(this.main.activeOption.customDevices[index]))
      this.currentDeviceIndex = index
      this.deviceDialogVisible = true
    },
    removeDevice(index) {
      this.$confirm('确定要删除这个设备吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.main.activeOption.customDevices.splice(index, 1)
        // 重置选中状态
        this.selectedDeviceIndex = -1
        // 强制更新视图
        this.$forceUpdate()
        // 触发父组件更新
        this.$nextTick(() => {
          this.$emit('update')
        })
        this.$message.success('删除成功')
      })
    },
    saveDevice() {
      if (!this.currentDevice.id || !this.currentDevice.name) {
        this.$message.error('请填写设备ID和名称')
        return
      }

      if (this.currentDeviceIndex === -1) {
        // 新增设备
        this.main.activeOption.customDevices.push(JSON.parse(JSON.stringify(this.currentDevice)))
      } else {
        // 编辑设备
        this.$set(this.main.activeOption.customDevices, this.currentDeviceIndex, JSON.parse(JSON.stringify(this.currentDevice)))
      }

      this.deviceDialogVisible = false
      // 强制更新视图
      this.$forceUpdate()
      // 触发父组件更新
      this.$nextTick(() => {
        this.$emit('update')
      })
      this.$message.success('保存成功')
    },
    openDeviceEditor() {
      this.deviceJsonData = JSON.stringify(this.main.activeOption.customDevices || [], null, 2)
      this.batchEditVisible = true
    },
    importFromJson() {
      this.$prompt('请输入JSON数据', '导入设备数据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea'
      }).then(({ value }) => {
        try {
          const devices = JSON.parse(value)
          if (Array.isArray(devices)) {
            this.deviceJsonData = JSON.stringify(devices, null, 2)
            this.$message.success('导入成功')
          } else {
            this.$message.error('数据格式错误，请输入数组格式的JSON')
          }
        } catch (error) {
          this.$message.error('JSON格式错误')
        }
      })
    },
    exportToJson() {
      const data = JSON.stringify(this.main.activeOption.customDevices || [], null, 2)
      const blob = new Blob([data], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'equipment-devices.json'
      a.click()
      URL.revokeObjectURL(url)
    },
    loadDefaultData() {
      // 加载默认的演示数据
      const defaultDevices = [
        { id: 'dev-01', name: '放板机', x: 50, y: 50, status: 'running', connections: ['dev-02'], tooltipData: { '型号': 'LDR-2000', '厂商': 'A-Tech', '投入日期': '2022-08-15' } },
        { id: 'dev-02', name: '激光打码', x: 200, y: 50, status: 'running', connections: ['dev-03'], icons: { camera: true }, tooltipData: { '型号': 'LM-500', '功率': '50W', '厂商': 'B-Laser' } },
        { id: 'dev-03', name: '线路前处理', x: 350, y: 50, status: 'running', connections: ['dev-04'], tooltipData: { '处理方式': '等离子清洗', '厂商': 'C-Process' } },
        { id: 'dev-04', name: '暂存机', x: 500, y: 50, status: 'idle', connections: ['dev-05'] },
        { id: 'dev-05', name: '收板机', x: 650, y: 50, status: 'running', connections: [] }
      ]
      this.deviceJsonData = JSON.stringify(defaultDevices, null, 2)
    },
    saveBatchEdit() {
      try {
        const devices = JSON.parse(this.deviceJsonData)
        if (Array.isArray(devices)) {
          this.$set(this.main.activeOption, 'customDevices', devices)
          this.batchEditVisible = false
          // 重置选中状态
          this.selectedDeviceIndex = -1
          // 强制更新视图
          this.$forceUpdate()
          // 触发父组件更新
          this.$nextTick(() => {
            this.$emit('update')
          })
          this.$message.success('保存成功')
        } else {
          this.$message.error('数据格式错误，请输入数组格式的JSON')
        }
      } catch (error) {
        this.$message.error('JSON格式错误')
      }
    },
    openTooltipEditor() {
      this.tooltipKeys = Object.keys(this.currentDevice.tooltipData || {})
      this.tooltipValues = Object.values(this.currentDevice.tooltipData || {})
      this.tooltipEditVisible = true
    },
    addTooltipItem() {
      this.tooltipKeys.push('')
      this.tooltipValues.push('')
    },
    removeTooltipItem(index) {
      this.tooltipKeys.splice(index, 1)
      this.tooltipValues.splice(index, 1)
    },
    saveTooltipData() {
      const tooltipData = {}
      this.tooltipKeys.forEach((key, index) => {
        if (key && this.tooltipValues[index]) {
          tooltipData[key] = this.tooltipValues[index]
        }
      })
      this.currentDevice.tooltipData = tooltipData
      this.tooltipEditVisible = false
    },
    getStatusText(status) {
      const statusMap = {
        running: '运行中',
        idle: '空闲',
        alarm: '报警'
      }
      return statusMap[status] || status
    },
    getStatusTagType(status) {
      const typeMap = {
        running: 'success',
        idle: 'warning',
        alarm: 'danger'
      }
      return typeMap[status] || 'info'
    },
    selectDevice(index) {
      this.selectedDeviceIndex = this.selectedDeviceIndex === index ? -1 : index
    },
    duplicateDevice(index) {
      const originalDevice = this.main.activeOption.customDevices[index]
      const newDevice = JSON.parse(JSON.stringify(originalDevice))
      newDevice.id = `${originalDevice.id}_copy_${Date.now()}`
      newDevice.name = `${originalDevice.name}_副本`
      newDevice.x = originalDevice.x + 50
      newDevice.y = originalDevice.y + 50
      this.main.activeOption.customDevices.push(newDevice)
      // 强制更新视图
      this.$forceUpdate()
      // 触发父组件更新
      this.$nextTick(() => {
        this.$emit('update')
      })
      this.$message.success('设备复制成功')
    },
    updateDevicePosition(index) {
      this.$message.success('设备位置已更新')
      // 触发组件重新渲染
      this.$forceUpdate()
    },
    updateDeviceStatus(index) {
      this.$message.success('设备状态已更新')
      // 触发组件重新渲染
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped>
.equipment-monitoring-option {
  padding: 20px;
}

.device-list {
  margin-top: 20px;
}

.device-item {
  margin-bottom: 15px;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-info p {
  margin: 5px 0;
  font-size: 12px;
  color: #666;
}

.tooltip-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

/* 设备管理面板样式 */
.device-management-panel {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;
  padding: 10px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-actions {
  display: flex;
  gap: 5px;
}

.device-list-container {
  max-height: 400px;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.device-item {
  transition: all 0.3s ease;
}

.device-selected {
  border: 2px solid #409eff !important;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.3) !important;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.device-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.device-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.device-status-dot.running {
  background-color: #67c23a;
}

.device-status-dot.idle {
  background-color: #e6a23c;
}

.device-status-dot.alarm {
  background-color: #f56c6c;
}

.device-name {
  font-weight: bold;
  color: #303133;
}

.device-actions {
  display: flex;
  gap: 5px;
}

.device-info {
  margin-top: 10px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
}

.info-label {
  color: #909399;
  font-weight: bold;
}

.info-value {
  color: #606266;
}

.quick-edit-area {
  margin-top: 15px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}
</style>
