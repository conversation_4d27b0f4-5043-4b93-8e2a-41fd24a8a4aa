<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备监控台插件演示</title>
    <link rel="stylesheet" href="../dist/css/index.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-family: Arial, sans-serif;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .demo-content {
            padding: 20px;
        }
        .demo-section {
            margin-bottom: 30px;
        }
        .demo-section h3 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .equipment-demo {
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .config-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .config-item {
            margin-bottom: 10px;
        }
        .config-item label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .config-item input, .config-item select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-left: 10px;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>设备监控台插件演示</h1>
            <p>基于Vue.js的工业设备实时监控可视化组件</p>
        </div>
        
        <div class="demo-content">
            <!-- 功能特性 -->
            <div class="demo-section">
                <h3>🚀 功能特性</h3>
                <ul class="feature-list">
                    <li>实时设备状态监控（运行中、空闲、报警）</li>
                    <li>设备连接关系可视化，支持流动动画效果</li>
                    <li>设备详情弹窗，包含参数、保养、报警信息</li>
                    <li>摄像头监控集成支持</li>
                    <li>鼠标悬停提示信息</li>
                    <li>自适应缩放和布局</li>
                    <li>可配置的轮询更新</li>
                    <li>支持自定义设备数据</li>
                    <li>响应式设计，支持多种屏幕尺寸</li>
                </ul>
            </div>

            <!-- 实时演示 -->
            <div class="demo-section">
                <h3>📺 实时演示</h3>
                <div id="equipment-monitoring-demo" class="equipment-demo"></div>
                
                <div class="config-panel">
                    <h4>配置选项</h4>
                    <div class="config-item">
                        <label>标题:</label>
                        <input type="text" id="title-input" value="产线设备实时监控">
                        <button class="btn" onclick="updateTitle()">更新标题</button>
                    </div>
                    <div class="config-item">
                        <label>背景颜色:</label>
                        <input type="color" id="bg-color" value="#1a1a1a">
                        <button class="btn" onclick="updateBackground()">更新背景</button>
                    </div>
                    <div class="config-item">
                        <label>轮询间隔:</label>
                        <select id="polling-interval">
                            <option value="1000">1秒</option>
                            <option value="3000" selected>3秒</option>
                            <option value="5000">5秒</option>
                            <option value="10000">10秒</option>
                        </select>
                        <button class="btn" onclick="updatePolling()">更新轮询</button>
                    </div>
                    <div class="config-item">
                        <button class="btn btn-success" onclick="loadSampleData()">加载示例数据</button>
                        <button class="btn" onclick="randomizeStatus()">随机化状态</button>
                        <button class="btn" onclick="resetDemo()">重置演示</button>
                    </div>
                </div>
            </div>

            <!-- 使用方法 -->
            <div class="demo-section">
                <h3>📖 使用方法</h3>
                <p>1. 在大屏设计器中，从左侧组件面板的"二次开发"分类中拖拽"设备监控台"组件到画布</p>
                <p>2. 在右侧配置面板中设置组件属性：</p>
                <div class="code-block">
{
  "title": "产线设备实时监控",
  "showHeader": true,
  "backgroundColor": "#1a1a1a",
  "textColor": "#ffffff",
  "enablePolling": true,
  "pollingInterval": 3000,
  "customDevices": [
    {
      "id": "dev-01",
      "name": "放板机",
      "x": 50,
      "y": 50,
      "status": "running",
      "connections": ["dev-02"],
      "icons": { "camera": false, "star": false },
      "tooltipData": {
        "型号": "LDR-2000",
        "厂商": "A-Tech",
        "投入日期": "2022-08-15"
      }
    }
  ]
}
                </div>
            </div>

            <!-- 配置说明 -->
            <div class="demo-section">
                <h3>⚙️ 配置说明</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">属性</th>
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">类型</th>
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">默认值</th>
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">title</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">String</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">产线设备实时监控</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">组件标题</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">showHeader</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">Boolean</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">true</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">是否显示标题</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">backgroundColor</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">String</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">#1a1a1a</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">背景颜色</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">enablePolling</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">Boolean</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">true</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">是否启用轮询更新</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">pollingInterval</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">Number</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">3000</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">轮询间隔（毫秒）</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">customDevices</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">Array</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">[]</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">自定义设备数据</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 设备状态说明 -->
            <div class="demo-section">
                <h3>🔧 设备状态说明</h3>
                <div style="display: flex; gap: 20px; margin-bottom: 15px;">
                    <div style="display: flex; align-items: center;">
                        <div style="width: 20px; height: 20px; background: #4CAF50; border-radius: 4px; margin-right: 8px;"></div>
                        <span>运行中 (running)</span>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <div style="width: 20px; height: 20px; background: #FFC107; border-radius: 4px; margin-right: 8px;"></div>
                        <span>空闲 (idle)</span>
                    </div>
                    <div style="display: flex; align-items: center;">
                        <div style="width: 20px; height: 20px; background: #F44336; border-radius: 4px; margin-right: 8px;"></div>
                        <span>报警 (alarm)</span>
                    </div>
                </div>
                <p>设备状态会通过颜色和图标进行区分，报警状态的设备会显示警告图标。</p>
            </div>
        </div>
    </div>

    <!-- 引入必要的库 -->
    <script src="../dist/lib/vue.min.js"></script>
    <script src="../dist/lib/element-ui.js"></script>
    <script src="../dist/components.js"></script>
    
    <script>
        // 演示用的Vue实例
        let demoApp;
        
        // 初始化演示
        function initDemo() {
            const demoOption = {
                title: "产线设备实时监控",
                showHeader: true,
                backgroundColor: "#1a1a1a",
                textColor: "#ffffff",
                headerFontSize: 24,
                headerColor: "#ffffff",
                headerAlign: "center",
                enablePolling: true,
                pollingInterval: 3000,
                customDevices: []
            };
            
            demoApp = new Vue({
                el: '#equipment-monitoring-demo',
                template: '<avue-echart-equipmentMonitoring :option="option"></avue-echart-equipmentMonitoring>',
                data: {
                    option: demoOption
                }
            });
        }
        
        // 更新标题
        function updateTitle() {
            const title = document.getElementById('title-input').value;
            demoApp.option.title = title;
        }
        
        // 更新背景
        function updateBackground() {
            const bgColor = document.getElementById('bg-color').value;
            demoApp.option.backgroundColor = bgColor;
        }
        
        // 更新轮询
        function updatePolling() {
            const interval = parseInt(document.getElementById('polling-interval').value);
            demoApp.option.pollingInterval = interval;
        }
        
        // 加载示例数据
        function loadSampleData() {
            const sampleDevices = [
                { id: 'dev-01', name: '放板机', x: 50, y: 50, status: 'running', connections: ['dev-02'], tooltipData: { '型号': 'LDR-2000', '厂商': 'A-Tech', '投入日期': '2022-08-15' } },
                { id: 'dev-02', name: '激光打码', x: 200, y: 50, status: 'running', connections: ['dev-03'], icons: { camera: true }, tooltipData: { '型号': 'LM-500', '功率': '50W', '厂商': 'B-Laser' } },
                { id: 'dev-03', name: '线路前处理', x: 350, y: 50, status: 'running', connections: ['dev-04'], tooltipData: { '处理方式': '等离子清洗', '厂商': 'C-Process' } },
                { id: 'dev-04', name: '暂存机', x: 500, y: 50, status: 'idle', connections: ['dev-05'] },
                { id: 'dev-05', name: '整平机', x: 650, y: 50, status: 'running', connections: ['dev-06'] },
                { id: 'dev-06', name: '涂布机', x: 800, y: 50, status: 'running', connections: ['dev-07'] },
                { id: 'dev-07', name: '隧道炉', x: 950, y: 50, status: 'running', connections: ['dev-08'] },
                { id: 'dev-08', name: '收板机', x: 1100, y: 50, status: 'running', connections: [] }
            ];
            demoApp.option.customDevices = sampleDevices;
        }
        
        // 随机化状态
        function randomizeStatus() {
            if (demoApp.option.customDevices.length === 0) {
                alert('请先加载示例数据');
                return;
            }
            
            demoApp.option.customDevices.forEach(device => {
                const rand = Math.random();
                if (rand < 0.15) { device.status = 'alarm'; }
                else if (rand < 0.4) { device.status = 'idle'; }
                else { device.status = 'running'; }
            });
            
            // 触发重新渲染
            demoApp.$forceUpdate();
        }
        
        // 重置演示
        function resetDemo() {
            demoApp.option.customDevices = [];
            document.getElementById('title-input').value = '产线设备实时监控';
            document.getElementById('bg-color').value = '#1a1a1a';
            document.getElementById('polling-interval').value = '3000';
            updateTitle();
            updateBackground();
            updatePolling();
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 等待Vue和组件加载完成
            setTimeout(initDemo, 1000);
        });
    </script>
</body>
</html>
