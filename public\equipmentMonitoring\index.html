<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备布局大屏监控</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>产线设备实时监控</h1>
        </header>
        <main class="device-layout" id="device-layout">
            <div id="zoom-container">
                <!-- 设备将由 JavaScript 动态生成 -->
            </div>
        </main>
    </div>

    <!-- 弹窗/模态窗口 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close-button">&times;</span>
            <h2 id="modal-title">设备详情</h2>
            <div class="modal-body">
                <div class="tabs">
                    <button class="tab-link active" data-tab="params">关键参数</button>
                    <button class="tab-link" data-tab="maintenance">保养信息</button>
                    <button class="tab-link" data-tab="alarm">报警详情</button>
                </div>
                <div id="params" class="tab-content active">
                    <p>温度: <span id="param-temp">--</span> &deg;C</p>
                    <p>压力: <span id="param-pressure">--</span> kPa</p>
                    <p>运行速度: <span id="param-speed">--</span> m/min</p>
                </div>
                <div id="maintenance" class="tab-content">
                    <p>上次保养时间: <span id="maint-date">--</span></p>
                    <p>保养负责人: <span id="maint-by">--</span></p>
                </div>
                <div id="alarm" class="tab-content">
                    <p>报警代码: <span id="alarm-code">--</span></p>
                    <p>报警信息: <span id="alarm-msg">--</span></p>
                    <p>发生时间: <span id="alarm-time">--</span></p>
                </div>
            </div>
        </div>
    </div>

    <!-- 摄像头视频弹窗 -->
    <div id="camera-modal" class="modal">
        <div class="modal-content camera-view">
            <span class="close-button">&times;</span>
            <h2 id="camera-title">实时监控画面</h2>
            <div class="modal-body">
                <!-- 实际项目中这里会嵌入视频流 -->
                <img src="https://via.placeholder.com/800x450.png?text=Camera+Feed" alt="Camera Feed" style="width: 100%;">
            </div>
        </div>
    </div>

    <script src="api.js"></script>
    <script src="script.js"></script>
</body>
</html>
