/* eslint-disable */
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api';
import 'monaco-editor/esm/vs/basic-languages/javascript/javascript.contribution';
import 'monaco-editor/esm/vs/basic-languages/sql/sql.contribution';
import beautifier from './json-beautifier'

function noop () { }

export { monaco };

export default {
  name: 'MonacoEditor',
  props: {
    diffEditor: { type: Boolean, default: false },      //是否使用diff模式
    width: { type: [String, Number], default: '100%' },
    height: { type: [String, Number], default: '100%' },
    original: String,       //只有在diff模式下有效
    value: [String, Object],
    language: { type: String, default: 'javascript' },
    theme: { type: String, default: 'vs-dark' },
    readOnly: { type: Boolean, default: false },
    options: { type: Object, default () { return {}; } },
    editorMounted: { type: Function, default: noop },
    editorBeforeMount: { type: Function, default: noop },
    keyIndex: { type: String }
  },

  data() {
    return {
      _isFormatting: false,  // 格式化状态标志
      _isValidating: false   // 验证状态标志
    };
  },

  watch: {
    options: {
      deep: true,
      handler (options) {
        this.editor && this.editor.updateOptions(options);
      }
    },

    keyIndex () {
      let data = this.value

      if (typeof data == 'object') {
        data = beautifier(data)
      }

      if (this.editor && data !== this._getValue()) {
        this._setValue(data)
      }
    },

    language () {
      if (!this.editor) return;
      if (this.diffEditor) {      //diff模式下更新language
        const { original, modified } = this.editor.getModel();
        monaco.editor.setModelLanguage(original, this.language);
        monaco.editor.setModelLanguage(modified, this.language);
      } else
        monaco.editor.setModelLanguage(this.editor.getModel(), this.language);
    },

    theme () {
      this.editor && monaco.editor.setTheme(this.theme);
    },

    style () {
      this.editor && this.$nextTick(() => {
        this.editor.layout();
      });
    },

    value (val) {
      if (this.editor && val !== this._getValue()) {
        this._setValue(val)
      }
    },
  },

  computed: {
    style () {
      return {
        width: !/^\d+$/.test(this.width) ? this.width : `${this.width}px`,
        height: !/^\d+$/.test(this.height) ? this.height : `${this.height}px`,
        position: 'relative'
      }
    }
  },

  mounted () {
    this.initMonaco();
  },

  beforeDestroy () {
    this.editor && this.editor.dispose();
  },

  render (h) {
    const fullScreen = this.options.fullScreen
    return h('div', { class: 'monaco_editor_container', style: this.style }, [
      fullScreen ? h('i', { class: 'el-icon-full-screen', style: { width: '1.2em', height: '1.2em', position: 'absolute', left: '0px', top: '0px', zIndex: '1', cursor: 'pointer' }, on: { click: this._handleFullScreen } }) : ''
    ])
  },

  methods: {
    initMonaco () {
      const { value, language, theme, readOnly, options } = this;
      Object.assign(options, this._editorBeforeMount());      //编辑器初始化前
      this.editor = monaco.editor[this.diffEditor ? 'createDiffEditor' : 'create'](this.$el, {
        value: (typeof value == 'string') ? value : beautifier(value),
        language: language,
        theme: theme,
        readOnly: readOnly,
        ...options
      });
      this.diffEditor && this._setModel(this.value, this.original);
      this._editorMounted(this.editor);      //编辑器初始化后
    },

    _handleFullScreen () {
      if (this.isMaximum) this.minEditor()
      else this.maxEditor()
    },
    // 放大
    maxEditor () {
      this.isMaximum = true
      let dom = this.$el
      this.originSize = {
        width: dom.clientWidth,
        height: dom.clientHeight
      }
      dom.classList.add('editor-fullscreen')
      this.editor.layout({
        height: document.body.clientHeight,
        width: document.body.clientWidth
      })

      document.addEventListener('keyup', (e) => {
        if (e.keyCode == 27) {
          this.minEditor()
        }
      })
    },
    // 缩小
    minEditor () {
      this.isMaximum = false
      let dom = this.$el
      dom.classList.remove('editor-fullscreen')
      this.editor.layout({
        height: this.originSize.height,
        width: this.originSize.width
      })
      document.removeEventListener('keyup', () => { })
    },

    _getEditor () {
      if (!this.editor) return null;
      return this.diffEditor ? this.editor.modifiedEditor : this.editor;
    },

    _setModel (value, original) {     //diff模式下设置model
      const { language } = this;
      const originalModel = monaco.editor.createModel(original, language);
      const modifiedModel = monaco.editor.createModel(value, language);
      this.editor.setModel({
        original: originalModel,
        modified: modifiedModel
      });
    },

    _setValue (value) {
      let editor = this._getEditor();
      if (editor) return editor.setValue(value);
    },

    _getValue () {
      let editor = this._getEditor();
      if (!editor) return '';
      return editor.getValue();
    },

    _editorBeforeMount () {
      const options = this.editorBeforeMount(monaco);
      return options || {};
    },

    _editorMounted (editor) {
      this.editorMounted(editor, monaco);
      if (this.diffEditor) {
        editor.onDidUpdateDiff((event) => {
          const value = this._getValue();
          this._emitChange(value, event);
        });
      } else {
        editor.onDidChangeModelContent(event => {
          const value = this._getValue();
          this._emitChange(value, event);
        });
      }
      // add by andy 2024-09-24
      editor.onDidBlurEditorText((event) => {
        this.$emit('blur', event);
        console.log("editor.onDidBlurEditorText")
      });
    },

    _emitChange (value, event) {
      this.$emit('change', value, event);
      this.$emit('input', value);
    },

    // 新增：格式化代码方法（优化版本，添加防重复点击保护）
    formatCode() {
      return new Promise((resolve, reject) => {
        try {
          const editor = this._getEditor();
          if (!editor) {
            reject(new Error('编辑器未初始化'));
            return;
          }

          // 防重复格式化保护
          if (this._isFormatting) {
            reject(new Error('格式化正在进行中，请稍候'));
            return;
          }

          this._isFormatting = true;

          // 使用Monaco编辑器内置的格式化功能
          editor.getAction('editor.action.formatDocument').run().then(() => {
            this._isFormatting = false;
            resolve('格式化成功');
          }).catch((error) => {
            // 如果内置格式化失败，使用自定义格式化
            try {
              const currentValue = editor.getValue();
              const formattedValue = this.customFormatJavaScript(currentValue);
              editor.setValue(formattedValue);
              this._isFormatting = false;
              resolve('格式化成功');
            } catch (customError) {
              this._isFormatting = false;
              reject(customError);
            }
          });
        } catch (error) {
          this._isFormatting = false;
          reject(error);
        }
      });
    },

    // 完全重新设计的JavaScript格式化方法 - 解决所有操作符问题
    customFormatJavaScript(code) {
      if (!code || code.trim() === '') {
        return code;
      }

      try {
        // 1. 保护字符串内容
        const stringPlaceholders = [];
        let stringIndex = 0;
        let formatted = code.replace(/(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g, (match) => {
          const placeholder = `__STRING_${stringIndex++}__`;
          stringPlaceholders.push({ placeholder, content: match });
          return placeholder;
        });

        // 2. 保护注释内容（修复版本 - 防止注释吞噬后续代码）
        const commentPlaceholders = [];
        let commentIndex = 0;

        // 使用安全的注释保护方法
        formatted = this.safeCommentProtection(formatted, commentPlaceholders, commentIndex);

        // 3. 保护箭头函数
        const arrowPlaceholders = [];
        let arrowIndex = 0;
        formatted = formatted.replace(/\s*=>\s*/g, () => {
          const placeholder = `__ARROW_${arrowIndex++}__`;
          arrowPlaceholders.push({ placeholder, content: ' => ' });
          return placeholder;
        });

        // 4. 基本的空白处理
        formatted = formatted.replace(/\s+/g, ' ').trim();

        // 5. 处理操作符（完全重写，解决操作符分割问题）
        formatted = formatted
          // 使用更精确的匹配，确保操作符不被分割
          .replace(/([^=!<>])===([^=])/g, '$1 === $2')  // 严格相等
          .replace(/([^=!])!==([^=])/g, '$1 !== $2')    // 严格不等
          .replace(/([^=!<>])<=([^=])/g, '$1 <= $2')    // 小于等于
          .replace(/([^=!<>])>=([^=])/g, '$1 >= $2')    // 大于等于
          .replace(/([^=!<>])==([^=])/g, '$1 == $2')    // 相等
          .replace(/([^=!])!=([^=])/g, '$1 != $2')      // 不等
          .replace(/([^=!<>])<([^=])/g, '$1 < $2')      // 小于
          .replace(/([^=!<>])>([^=])/g, '$1 > $2')      // 大于
          .replace(/([^&])&&([^&])/g, '$1 && $2')       // 逻辑与
          .replace(/([^|])\|\|([^|])/g, '$1 || $2')     // 逻辑或
          // 算术操作符
          .replace(/([^+])\+([^+=])/g, '$1 + $2')       // 加法
          .replace(/([^-])-([^-=])/g, '$1 - $2')        // 减法
          .replace(/([^*])\*([^*=])/g, '$1 * $2')       // 乘法
          .replace(/([^%])%([^=])/g, '$1 % $2')         // 模运算
          // 赋值操作符
          .replace(/([a-zA-Z_$][a-zA-Z0-9_$]*|\]|\))=([^=])/g, '$1 = $2');

        // 5. 处理标点符号
        formatted = formatted
          // 逗号后加空格
          .replace(/,(?!\s)/g, ', ')
          // 分号后换行
          .replace(/;(?!\s*\n)/g, ';\n')
          // 大括号处理
          .replace(/\{(?!\s*\n)/g, ' {\n')
          .replace(/\}(?!\s*\n)/g, '\n}')
          // 小括号处理
          .replace(/\s*\(\s*/g, '(')
          .replace(/\s*\)\s*/g, ')');

        // 6. 最终清理（避免破坏已保护的内容）
        formatted = formatted
          .replace(/\s*{\s*/g, ' {\n')
          .replace(/\s*}\s*/g, '\n}')
          .replace(/\s*;\s*/g, ';\n')
          .replace(/\s*,\s*/g, ', ');

        // 7. 恢复箭头函数
        arrowPlaceholders.forEach(({ placeholder, content }) => {
          formatted = formatted.replace(placeholder, content);
        });

        // 8. 恢复注释内容
        commentPlaceholders.forEach(({ placeholder, content }) => {
          formatted = formatted.replace(placeholder, content);
        });

        // 9. 恢复字符串内容
        stringPlaceholders.forEach(({ placeholder, content }) => {
          formatted = formatted.replace(placeholder, content);
        });

        // 添加适当的缩进
        const lines = formatted.split('\n');
        let indentLevel = 0;
        const indentSize = 4;

        const formattedLines = lines.map(line => {
          line = line.trim();
          if (line === '') return '';

          // 减少缩进
          if (line.includes('}')) {
            indentLevel = Math.max(0, indentLevel - 1);
          }

          const indentedLine = ' '.repeat(indentLevel * indentSize) + line;

          // 增加缩进
          if (line.includes('{')) {
            indentLevel++;
          }

          return indentedLine;
        });

        return formattedLines.join('\n');
      } catch (error) {
        throw new Error(`格式化失败: ${error.message}`);
      }
    },

    // 验证代码语法（优化版本，添加防重复调用保护）
    validateSyntax() {
      return new Promise((resolve, reject) => {
        try {
          const editor = this._getEditor();
          if (!editor) {
            reject(new Error('编辑器未初始化'));
            return;
          }

          // 防重复验证保护
          if (this._isValidating) {
            reject(new Error('语法验证正在进行中，请稍候'));
            return;
          }

          this._isValidating = true;

          const code = editor.getValue();
          if (!code || code.trim() === '') {
            this._isValidating = false;
            reject(new Error('代码内容为空'));
            return;
          }

          // 获取Monaco编辑器的语法错误标记
          const model = editor.getModel();
          const markers = monaco.editor.getModelMarkers({ resource: model.uri });

          if (markers.length > 0) {
            const errors = markers.map(marker =>
              `第${marker.startLineNumber}行: ${marker.message}`
            ).join('\n');
            this._isValidating = false;
            reject(new Error(`语法错误:\n${errors}`));
          } else {
            this._isValidating = false;
            resolve('语法验证通过');
          }
        } catch (error) {
          this._isValidating = false;
          reject(error);
        }
      });
    },

    // 安全的注释保护方法 - 防止注释吞噬后续代码
    safeCommentProtection(code, commentPlaceholders, startIndex) {
      let result = '';
      let i = 0;
      let commentIndex = startIndex;

      while (i < code.length) {
        // 检查多行注释
        if (i < code.length - 1 && code[i] === '/' && code[i + 1] === '*') {
          let commentStart = i;
          i += 2; // 跳过 /*

          // 查找注释结束
          while (i < code.length - 1) {
            if (code[i] === '*' && code[i + 1] === '/') {
              i += 2; // 跳过 */
              break;
            }
            i++;
          }

          const comment = code.substring(commentStart, i);
          const placeholder = `__COMMENT_${commentIndex++}__`;
          commentPlaceholders.push({ placeholder, content: comment });
          result += placeholder;
          continue;
        }

        // 检查单行注释
        if (i < code.length - 1 && code[i] === '/' && code[i + 1] === '/') {
          let commentStart = i;
          i += 2; // 跳过 //

          // 查找注释结束 - 只到行尾或分号
          while (i < code.length) {
            const char = code[i];
            // 遇到换行符、分号、或者可能的代码结构就停止
            if (char === '\n' || char === '\r' ||
                char === ';' ||
                (char === ' ' && i + 1 < code.length && /[a-zA-Z_$]/.test(code[i + 1]))) {
              break;
            }
            i++;
          }

          const comment = code.substring(commentStart, i);
          const placeholder = `__COMMENT_${commentIndex++}__`;
          commentPlaceholders.push({ placeholder, content: comment });
          result += placeholder;
          continue;
        }

        // 普通字符
        result += code[i];
        i++;
      }

      return result;
    }
  }
}

