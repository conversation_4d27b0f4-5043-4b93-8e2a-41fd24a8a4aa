<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内存泄漏测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .button-group {
            margin: 15px 0;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #3498db;
            color: white;
        }
        .btn-success {
            background: #27ae60;
            color: white;
        }
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .status-info {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .memory-chart {
            width: 100%;
            height: 200px;
            border: 1px solid #ddd;
            margin: 10px 0;
            position: relative;
            background: #f9f9f9;
        }
        .log-area {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 内存泄漏测试页面</h1>
            <p>用于测试 CI.Web.Plugins.Bulletin 项目的内存使用情况</p>
        </div>

        <!-- 内存监控区域 -->
        <div class="test-section">
            <h3>📊 内存监控</h3>
            <div class="button-group">
                <button class="btn btn-primary" onclick="startMemoryMonitor()">开始监控</button>
                <button class="btn btn-warning" onclick="stopMemoryMonitor()">停止监控</button>
                <button class="btn btn-success" onclick="getMemoryReport()">获取报告</button>
                <button class="btn btn-danger" onclick="forceGC()">强制垃圾回收</button>
            </div>
            <div id="memoryStatus" class="status status-info">
                点击"开始监控"开始内存监控...
            </div>
            <div class="memory-chart" id="memoryChart">
                <canvas id="memoryCanvas" width="100%" height="200"></canvas>
            </div>
        </div>

        <!-- 压力测试区域 -->
        <div class="test-section">
            <h3>⚡ 压力测试</h3>
            <div class="button-group">
                <button class="btn btn-primary" onclick="startStressTest()">开始压力测试</button>
                <button class="btn btn-warning" onclick="stopStressTest()">停止测试</button>
                <button class="btn btn-success" onclick="createComponents()">创建组件</button>
                <button class="btn btn-danger" onclick="destroyComponents()">销毁组件</button>
            </div>
            <div id="stressStatus" class="status status-info">
                准备进行压力测试...
            </div>
        </div>

        <!-- 定时器测试区域 -->
        <div class="test-section">
            <h3>⏰ 定时器测试</h3>
            <div class="button-group">
                <button class="btn btn-primary" onclick="createTimers()">创建定时器</button>
                <button class="btn btn-warning" onclick="clearAllTimers()">清理定时器</button>
                <button class="btn btn-success" onclick="getTimerStats()">定时器统计</button>
            </div>
            <div id="timerStatus" class="status status-info">
                定时器测试准备就绪...
            </div>
        </div>

        <!-- 日志区域 -->
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div class="button-group">
                <button class="btn btn-warning" onclick="clearLog()">清空日志</button>
                <button class="btn btn-success" onclick="exportLog()">导出日志</button>
            </div>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let memoryMonitorInterval = null;
        let stressTestInterval = null;
        let testTimers = [];
        let testComponents = [];
        let memoryHistory = [];
        let logMessages = [];

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            logMessages.push(logMessage);
            
            const logArea = document.getElementById('logArea');
            logArea.textContent += logMessage + '\n';
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(logMessage);
        }

        // 内存监控功能
        function startMemoryMonitor() {
            if (memoryMonitorInterval) {
                log('内存监控已在运行中', 'warning');
                return;
            }

            log('开始内存监控...', 'info');
            memoryHistory = [];
            
            memoryMonitorInterval = setInterval(() => {
                if (performance.memory) {
                    const memory = {
                        timestamp: Date.now(),
                        used: performance.memory.usedJSHeapSize,
                        total: performance.memory.totalJSHeapSize,
                        limit: performance.memory.jsHeapSizeLimit
                    };
                    
                    memoryHistory.push(memory);
                    
                    // 保持最近50条记录
                    if (memoryHistory.length > 50) {
                        memoryHistory.shift();
                    }
                    
                    updateMemoryDisplay(memory);
                    drawMemoryChart();
                    
                    // 检查内存使用率
                    const usagePercent = (memory.used / memory.limit) * 100;
                    if (usagePercent > 80) {
                        log(`⚠️ 内存使用率过高: ${usagePercent.toFixed(2)}%`, 'warning');
                    }
                }
            }, 1000);
            
            document.getElementById('memoryStatus').className = 'status status-info';
            document.getElementById('memoryStatus').textContent = '内存监控运行中...';
        }

        function stopMemoryMonitor() {
            if (memoryMonitorInterval) {
                clearInterval(memoryMonitorInterval);
                memoryMonitorInterval = null;
                log('内存监控已停止', 'info');
                
                document.getElementById('memoryStatus').className = 'status status-warning';
                document.getElementById('memoryStatus').textContent = '内存监控已停止';
            }
        }

        function updateMemoryDisplay(memory) {
            const usedMB = (memory.used / 1024 / 1024).toFixed(2);
            const totalMB = (memory.total / 1024 / 1024).toFixed(2);
            const limitMB = (memory.limit / 1024 / 1024).toFixed(2);
            const usagePercent = ((memory.used / memory.limit) * 100).toFixed(2);
            
            const statusElement = document.getElementById('memoryStatus');
            statusElement.textContent = `内存使用: ${usedMB}MB / ${limitMB}MB (${usagePercent}%)`;
            
            if (usagePercent > 80) {
                statusElement.className = 'status status-error';
            } else if (usagePercent > 60) {
                statusElement.className = 'status status-warning';
            } else {
                statusElement.className = 'status status-info';
            }
        }

        function drawMemoryChart() {
            const canvas = document.getElementById('memoryCanvas');
            const ctx = canvas.getContext('2d');
            const width = canvas.width = canvas.offsetWidth;
            const height = canvas.height = 200;
            
            ctx.clearRect(0, 0, width, height);
            
            if (memoryHistory.length < 2) return;
            
            // 绘制网格
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 1;
            for (let i = 0; i <= 10; i++) {
                const y = (height / 10) * i;
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }
            
            // 绘制内存使用曲线
            ctx.strokeStyle = '#3498db';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            const maxMemory = Math.max(...memoryHistory.map(m => m.used));
            const minMemory = Math.min(...memoryHistory.map(m => m.used));
            const range = maxMemory - minMemory || 1;
            
            memoryHistory.forEach((memory, index) => {
                const x = (width / (memoryHistory.length - 1)) * index;
                const y = height - ((memory.used - minMemory) / range) * height;
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            
            ctx.stroke();
        }

        function getMemoryReport() {
            if (performance.memory) {
                const memory = performance.memory;
                const report = {
                    used: (memory.usedJSHeapSize / 1024 / 1024).toFixed(2) + 'MB',
                    total: (memory.totalJSHeapSize / 1024 / 1024).toFixed(2) + 'MB',
                    limit: (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2) + 'MB',
                    usage: ((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100).toFixed(2) + '%'
                };
                
                log(`内存报告: ${JSON.stringify(report, null, 2)}`, 'info');
            } else {
                log('浏览器不支持 performance.memory API', 'warning');
            }
        }

        function forceGC() {
            if (window.gc) {
                window.gc();
                log('强制垃圾回收已执行', 'info');
            } else {
                log('浏览器不支持手动垃圾回收（需要启动时添加 --expose-gc 参数）', 'warning');
            }
        }

        // 压力测试功能
        function startStressTest() {
            log('开始压力测试...', 'info');
            
            stressTestInterval = setInterval(() => {
                // 创建大量对象
                const largeArray = new Array(10000).fill(0).map((_, i) => ({
                    id: i,
                    data: new Array(100).fill(Math.random()),
                    timestamp: Date.now()
                }));
                
                // 模拟DOM操作
                const div = document.createElement('div');
                div.innerHTML = '<span>测试数据</span>'.repeat(1000);
                document.body.appendChild(div);
                
                setTimeout(() => {
                    document.body.removeChild(div);
                }, 100);
                
                log(`压力测试执行中... 创建了 ${largeArray.length} 个对象`, 'info');
            }, 2000);
            
            document.getElementById('stressStatus').textContent = '压力测试运行中...';
        }

        function stopStressTest() {
            if (stressTestInterval) {
                clearInterval(stressTestInterval);
                stressTestInterval = null;
                log('压力测试已停止', 'info');
                
                document.getElementById('stressStatus').textContent = '压力测试已停止';
            }
        }

        // 定时器测试功能
        function createTimers() {
            for (let i = 0; i < 10; i++) {
                const timer = setInterval(() => {
                    // 模拟定时器工作
                    Math.random();
                }, 100);
                testTimers.push(timer);
            }
            
            log(`创建了 ${testTimers.length} 个定时器`, 'info');
            getTimerStats();
        }

        function clearAllTimers() {
            testTimers.forEach(timer => clearInterval(timer));
            testTimers = [];
            log('所有测试定时器已清理', 'info');
            getTimerStats();
        }

        function getTimerStats() {
            const stats = `活跃定时器数量: ${testTimers.length}`;
            log(stats, 'info');
            document.getElementById('timerStatus').textContent = stats;
        }

        // 组件测试功能
        function createComponents() {
            for (let i = 0; i < 5; i++) {
                const component = {
                    id: `component_${Date.now()}_${i}`,
                    element: document.createElement('div'),
                    timer: setInterval(() => {
                        // 模拟组件工作
                    }, 500)
                };
                
                component.element.textContent = `测试组件 ${component.id}`;
                document.body.appendChild(component.element);
                testComponents.push(component);
            }
            
            log(`创建了 ${testComponents.length} 个测试组件`, 'info');
        }

        function destroyComponents() {
            testComponents.forEach(component => {
                clearInterval(component.timer);
                if (component.element.parentNode) {
                    component.element.parentNode.removeChild(component.element);
                }
            });
            testComponents = [];
            log('所有测试组件已销毁', 'info');
        }

        // 日志功能
        function clearLog() {
            logMessages = [];
            document.getElementById('logArea').textContent = '';
            log('日志已清空', 'info');
        }

        function exportLog() {
            const logContent = logMessages.join('\n');
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `memory-test-log-${new Date().toISOString().slice(0, 19)}.txt`;
            a.click();
            
            URL.revokeObjectURL(url);
            log('日志已导出', 'info');
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            log('内存泄漏测试页面已加载', 'info');
            log('建议：先开始内存监控，然后进行各种测试', 'info');
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            stopMemoryMonitor();
            stopStressTest();
            clearAllTimers();
            destroyComponents();
        });
    </script>
</body>
</html>
