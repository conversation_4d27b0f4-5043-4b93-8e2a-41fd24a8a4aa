<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>格式化代码优化验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-section h2 {
            color: #555;
            margin-top: 0;
        }
        .console-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .btn {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn:hover {
            background: #66b1ff;
        }
        .btn:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #e1f3d8;
            color: #67c23a;
            border: 1px solid #b3d8a4;
        }
        .status.warning {
            background: #fdf6ec;
            color: #e6a23c;
            border: 1px solid #f0c78a;
        }
        .status.error {
            background: #fef0f0;
            color: #f56c6c;
            border: 1px solid #f5a9a9;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            padding: 15px;
            border-radius: 6px;
        }
        .before {
            background: #fef0f0;
            border: 1px solid #f5a9a9;
        }
        .after {
            background: #e1f3d8;
            border: 1px solid #b3d8a4;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 格式化代码优化验证</h1>
        
        <div class="test-section">
            <h2>测试控制</h2>
            <button id="runTest" class="btn">运行测试</button>
            <button id="clearConsole" class="btn">清空控制台</button>
            <div id="testStatus" class="status">准备就绪</div>
        </div>

        <div class="test-section">
            <h2>优化对比</h2>
            <div class="comparison">
                <div class="comparison-item before">
                    <h3>❌ 优化前（有问题）</h3>
                    <ul>
                        <li>没有防重复点击保护</li>
                        <li>可能同时进行多个格式化操作</li>
                        <li>状态管理混乱</li>
                        <li>用户体验差</li>
                    </ul>
                    <div class="code-block">
async formatCode() {
  // 直接开始格式化，没有保护
  await this.doFormat();
}
                    </div>
                </div>
                <div class="comparison-item after">
                    <h3>✅ 优化后（已修复）</h3>
                    <ul>
                        <li>添加了防重复点击保护</li>
                        <li>同时只能有一个格式化操作</li>
                        <li>清晰的状态管理</li>
                        <li>用户友好的提示</li>
                    </ul>
                    <div class="code-block">
async formatCode() {
  if (<span class="highlight">this.isFormatting</span>) {
    this.formatClickCount++;
    return; // 忽略重复点击
  }
  <span class="highlight">this.isFormatting = true;</span>
  try {
    await this.doFormat();
  } finally {
    <span class="highlight">this.isFormatting = false;</span>
  }
}
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>测试结果</h2>
            <div id="consoleOutput" class="console-output">等待测试运行...</div>
        </div>

        <div class="test-section">
            <h2>优化要点</h2>
            <h3>1. 状态管理</h3>
            <ul>
                <li><code>isFormatting</code>: 标记格式化是否正在进行</li>
                <li><code>formatClickCount</code>: 记录重复点击次数</li>
            </ul>
            
            <h3>2. 用户体验</h3>
            <ul>
                <li>按钮禁用状态：<code>:disabled="isFormatting"</code></li>
                <li>加载状态：<code>:loading="isFormatting"</code></li>
                <li>动态文本：<code>{{ isFormatting ? '格式化中...' : '格式化代码' }}</code></li>
            </ul>
            
            <h3>3. 错误处理</h3>
            <ul>
                <li>使用 <code>try-finally</code> 确保状态重置</li>
                <li>友好的错误提示信息</li>
                <li>重复点击计数和提示</li>
            </ul>
        </div>
    </div>

    <script src="format-optimization-test.js"></script>
    <script>
        let consoleOutput = document.getElementById('consoleOutput');
        let testStatus = document.getElementById('testStatus');
        let runTestBtn = document.getElementById('runTest');
        let clearConsoleBtn = document.getElementById('clearConsole');

        // 重写console.log来显示在页面上
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        
        function logToPage(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : 'ℹ️';
            consoleOutput.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            
            // 同时输出到真实控制台
            if (type === 'error') {
                originalConsoleError(message);
            } else {
                originalConsoleLog(message);
            }
        }

        console.log = (...args) => logToPage(args.join(' '), 'log');
        console.error = (...args) => logToPage(args.join(' '), 'error');
        console.warn = (...args) => logToPage(args.join(' '), 'warn');

        // 运行测试按钮
        runTestBtn.addEventListener('click', async () => {
            runTestBtn.disabled = true;
            testStatus.textContent = '测试运行中...';
            testStatus.className = 'status warning';
            
            try {
                await testFormatCode();
                testStatus.textContent = '测试完成 ✅';
                testStatus.className = 'status success';
            } catch (error) {
                testStatus.textContent = `测试失败: ${error.message}`;
                testStatus.className = 'status error';
                console.error('测试运行出错:', error);
            } finally {
                runTestBtn.disabled = false;
            }
        });

        // 清空控制台按钮
        clearConsoleBtn.addEventListener('click', () => {
            consoleOutput.textContent = '';
            testStatus.textContent = '控制台已清空';
            testStatus.className = 'status';
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            logToPage('页面加载完成，准备运行测试');
            logToPage('点击"运行测试"按钮开始验证优化效果');
        });
    </script>
</body>
</html>
