<template>
  <div class="equipment-monitoring-container" :style="containerStyle">
    <header v-if="option.showHeader" class="equipment-header">
      <h1 :style="headerStyle">{{ option.title || '产线设备实时监控' }}</h1>
    </header>
    <main class="device-layout" ref="deviceLayout" :style="layoutStyle">
      <div ref="zoomContainer" class="zoom-container">
        <!-- 设备将由 JavaScript 动态生成 -->
      </div>
    </main>

    <!-- 弹窗/模态窗口 -->
    <div v-if="showModal" class="modal" @click="closeModal">
      <div class="modal-content" @click.stop>
        <span class="close-button" @click="closeModal">&times;</span>
        <h2>{{ modalTitle }}</h2>
        <div class="modal-body">
          <div class="tabs">
            <button 
              v-for="tab in tabs" 
              :key="tab.key"
              class="tab-link" 
              :class="{ active: activeTab === tab.key }"
              @click="activeTab = tab.key"
            >
              {{ tab.label }}
            </button>
          </div>
          <div v-if="activeTab === 'params'" class="tab-content active">
            <p v-for="param in currentDeviceParams" :key="param.key">
              {{ param.label }}: <span>{{ param.value }}</span> {{ param.unit }}
            </p>
          </div>
          <div v-if="activeTab === 'maintenance'" class="tab-content active">
            <p>上次保养时间: <span>{{ currentDeviceMaintenance.date }}</span></p>
            <p>保养负责人: <span>{{ currentDeviceMaintenance.by }}</span></p>
          </div>
          <div v-if="activeTab === 'alarm'" class="tab-content active">
            <p>报警代码: <span>{{ currentDeviceAlarm.code }}</span></p>
            <p>报警信息: <span>{{ currentDeviceAlarm.message }}</span></p>
            <p>发生时间: <span>{{ currentDeviceAlarm.time }}</span></p>
          </div>
        </div>
      </div>
    </div>

    <!-- 摄像头视频弹窗 -->
    <div v-if="showCameraModal" class="modal" @click="closeCameraModal">
      <div class="modal-content camera-view" @click.stop>
        <span class="close-button" @click="closeCameraModal">&times;</span>
        <h2>{{ cameraTitle }}</h2>
        <div class="modal-body">
          <!-- 实际项目中这里会嵌入视频流 -->
          <img src="https://via.placeholder.com/800x450.png?text=Camera+Feed" alt="Camera Feed" style="width: 100%;">
        </div>
      </div>
    </div>

    <!-- 工具提示 -->
    <div 
      v-if="showTooltip" 
      class="device-tooltip" 
      :style="tooltipStyle"
    >
      <div v-for="(value, key) in tooltipData" :key="key">
        <strong>{{ key }}:</strong> {{ value }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'equipmentMonitoring',
  props: {
    option: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      devices: [],
      showModal: false,
      showCameraModal: false,
      showTooltip: false,
      modalTitle: '',
      cameraTitle: '',
      activeTab: 'params',
      currentDevice: null,
      tooltipData: {},
      tooltipPosition: { x: 0, y: 0 },
      pollingTimer: null,
      tabs: [
        { key: 'params', label: '关键参数' },
        { key: 'maintenance', label: '保养信息' },
        { key: 'alarm', label: '报警详情' }
      ]
    }
  },
  computed: {
    containerStyle() {
      return {
        width: '100%',
        height: '100%',
        backgroundColor: this.option.backgroundColor || '#1a1a1a',
        color: this.option.textColor || '#ffffff',
        fontFamily: this.option.fontFamily || 'Arial, sans-serif'
      }
    },
    headerStyle() {
      return {
        fontSize: this.option.headerFontSize || '24px',
        color: this.option.headerColor || '#ffffff',
        textAlign: this.option.headerAlign || 'center'
      }
    },
    tooltipStyle() {
      return {
        position: 'absolute',
        left: this.tooltipPosition.x + 'px',
        top: this.tooltipPosition.y + 'px',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '8px 12px',
        borderRadius: '4px',
        pointerEvents: 'none',
        zIndex: 1000,
        fontSize: '12px'
      }
    },
    layoutStyle() {
      return {
        width: '100%',
        height: this.option.showHeader ? 'calc(100% - 80px)' : '100%',
        overflow: 'hidden'
      }
    },
    currentDeviceParams() {
      if (!this.currentDevice) return []
      return [
        { key: 'temp', label: '温度', value: (Math.random() * 20 + 60).toFixed(1), unit: '°C' },
        { key: 'pressure', label: '压力', value: (Math.random() * 50 + 100).toFixed(1), unit: 'kPa' },
        { key: 'speed', label: '运行速度', value: (Math.random() * 10 + 5).toFixed(1), unit: 'm/min' }
      ]
    },
    currentDeviceMaintenance() {
      return {
        date: '2024-01-15',
        by: '张工程师'
      }
    },
    currentDeviceAlarm() {
      return {
        code: 'E001',
        message: '温度超限',
        time: '2024-01-17 14:30:25'
      }
    }
  },
  mounted() {
    this.initializeLayout()
    this.startPolling()
  },
  beforeDestroy() {
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer)
    }
    window.removeEventListener('resize', this.updateZoom)
  },
  methods: {
    async initializeLayout() {
      try {
        const deviceData = await this.getDeviceData()
        this.devices = deviceData
        this.$nextTick(() => {
          this.renderDevices()
          this.updateZoom()
          window.addEventListener('resize', this.updateZoom)
        })
      } catch (error) {
        console.error('Failed to load device data:', error)
      }
    },
    async getDeviceData() {
      // 如果有自定义数据源，使用自定义数据
      if (this.option.customDevices && this.option.customDevices.length > 0) {
        return this.option.customDevices
      }
      
      // 否则使用默认的模拟数据
      return this.getMockDeviceData()
    },
    getMockDeviceData() {
      const mockData = [
        { id: 'dev-01', name: '放板机', x: 50, y: 50, status: 'running', connections: ['dev-02'], tooltipData: { '型号': 'LDR-2000', '厂商': 'A-Tech', '投入日期': '2022-08-15' } },
        { id: 'dev-02', name: '激光打码', x: 200, y: 50, status: 'running', connections: ['dev-03'], icons: { camera: true }, tooltipData: { '型号': 'LM-500', '功率': '50W', '厂商': 'B-Laser' } },
        { id: 'dev-03', name: '线路前处理', x: 350, y: 50, status: 'running', connections: ['dev-04'], tooltipData: { '处理方式': '等离子清洗', '厂商': 'C-Process' } },
        { id: 'dev-04', name: '暂存机', x: 500, y: 50, status: 'idle', connections: ['dev-05'] },
        { id: 'dev-05', name: '整平机', x: 650, y: 50, status: 'running', connections: ['dev-06'] },
        { id: 'dev-06', name: '涂布机', x: 800, y: 50, status: 'running', connections: ['dev-07'] },
        { id: 'dev-07', name: '隧道炉', x: 950, y: 50, status: 'running', connections: ['dev-08'] },
        { id: 'dev-08', name: '整平机', x: 1100, y: 50, status: 'running', connections: ['dev-09'] },
        { id: 'dev-09', name: '暂存机', x: 1250, y: 50, status: 'idle', connections: ['dev-10'] },
        { id: 'dev-10', name: '线路DI', x: 1400, y: 50, status: 'running', connections: ['dev-11'], icons: { camera: true } },
        { id: 'dev-11', name: 'NG暂存机', x: 1550, y: 50, status: 'idle', connections: ['dev-12'] },
        { id: 'dev-12', name: '显影蚀刻退膜', x: 1700, y: 50, status: 'running', connections: ['dev-13'], icons: { star: true } },
        { id: 'dev-13', name: 'AOI', x: 1850, y: 50, status: 'running', connections: ['dev-14'] },
        { id: 'dev-14', name: '转角机1', x: 1850, y: 180, status: 'running', connections: ['dev-15'] },
        { id: 'dev-15', name: '侧边一体机', x: 1850, y: 310, status: 'running', connections: ['dev-16'] },
        { id: 'dev-16', name: '打靶', x: 1850, y: 440, status: 'running', connections: ['dev-17'] },
        { id: 'dev-17', name: '转角机3', x: 1850, y: 570, status: 'running', connections: ['dev-18'] },
        { id: 'dev-18', name: '阻焊前处理', x: 1700, y: 570, status: 'running', connections: ['dev-19'] },
        { id: 'dev-19', name: '暂存机', x: 1550, y: 570, status: 'idle', connections: ['dev-20'] },
        { id: 'dev-20', name: '阻焊涂布', x: 1400, y: 570, status: 'running', connections: ['dev-21'] },
        { id: 'dev-21', name: '隧道炉', x: 1250, y: 570, status: 'running', connections: ['dev-22'] },
        { id: 'dev-22', name: '先进先出暂存机', x: 1100, y: 570, status: 'idle', connections: ['dev-23'] },
        { id: 'dev-23', name: '暂存机', x: 950, y: 570, status: 'running', connections: ['dev-24'], icons: { camera: true } },
        { id: 'dev-24', name: '阻焊DI', x: 800, y: 570, status: 'idle', connections: ['dev-25'] },
        { id: 'dev-25', name: 'NG暂存机', x: 650, y: 570, status: 'running', connections: ['dev-26'], icons: { star: true } },
        { id: 'dev-26', name: '显影', x: 500, y: 570, status: 'idle', connections: ['dev-27'] },
        { id: 'dev-27', name: '暂存机', x: 500, y: 440, status: 'idle', connections: ['dev-28'] },
        { id: 'dev-28', name: '文字', x: 350, y: 440, status: 'running', connections: ['dev-29'] },
        { id: 'dev-29', name: '隧道炉', x: 200, y: 440, status: 'running', connections: ['dev-30'] },
        { id: 'dev-30', name: '收板机', x: 50, y: 440, status: 'running', connections: [] }
      ]
      
      // 随机化状态
      mockData.forEach(device => {
        const rand = Math.random()
        if (rand < 0.15) { device.status = 'alarm' }
        else if (rand < 0.4) { device.status = 'idle' }
        else { device.status = 'running' }
      })
      
      return Promise.resolve(mockData)
    },
    renderDevices() {
      const zoomContainer = this.$refs.zoomContainer
      if (!zoomContainer) return

      // 清空容器
      zoomContainer.innerHTML = ''

      // 渲染设备
      this.devices.forEach(device => {
        const devElement = document.createElement('div')
        devElement.className = `device ${device.status}`
        devElement.id = device.id
        devElement.style.left = `${device.x}px`
        devElement.style.top = `${device.y}px`
        devElement.style.position = 'absolute'
        devElement.style.width = '120px'
        devElement.style.height = '70px'
        devElement.style.border = '2px solid'
        devElement.style.borderRadius = '8px'
        devElement.style.display = 'flex'
        devElement.style.flexDirection = 'column'
        devElement.style.justifyContent = 'center'
        devElement.style.alignItems = 'center'
        devElement.style.cursor = 'pointer'
        devElement.style.fontSize = '12px'
        devElement.style.fontWeight = 'bold'
        devElement.style.textAlign = 'center'
        devElement.style.transition = 'all 0.3s ease'

        // 根据状态设置颜色
        if (device.status === 'running') {
          devElement.style.backgroundColor = '#4CAF50'
          devElement.style.borderColor = '#45a049'
          devElement.style.color = this.option.textColor || 'white'
        } else if (device.status === 'idle') {
          devElement.style.backgroundColor = '#FFC107'
          devElement.style.borderColor = '#e0a800'
          devElement.style.color = this.option.textColor || 'black'
        } else if (device.status === 'alarm') {
          devElement.style.backgroundColor = '#F44336'
          devElement.style.borderColor = '#da190b'
          devElement.style.color = this.option.textColor || 'white'
        }

        const name = document.createElement('div')
        name.className = 'device-name'
        name.textContent = device.name
        name.style.marginBottom = '5px'

        const icons = document.createElement('div')
        icons.className = 'device-icons'
        icons.style.display = 'flex'
        icons.style.gap = '5px'

        let iconHtml = ''
        if (device.status === 'alarm') {
          iconHtml += '<i class="el-icon-warning" style="color: yellow;"></i>'
        }
        if (device.icons?.camera) {
          iconHtml += '<i class="el-icon-video-camera camera-btn" style="color: #2196F3;"></i>'
        }
        if (device.icons?.star) {
          iconHtml += '<i class="el-icon-star-on" style="color: gold;"></i>'
        }
        icons.innerHTML = iconHtml

        devElement.appendChild(name)
        devElement.appendChild(icons)

        // 添加事件监听器
        if (device.tooltipData) {
          devElement.addEventListener('mouseover', (event) => {
            this.showTooltip = true
            this.tooltipData = device.tooltipData
            this.updateTooltipPosition(event)
          })

          devElement.addEventListener('mouseout', () => {
            this.showTooltip = false
          })

          devElement.addEventListener('mousemove', (event) => {
            this.updateTooltipPosition(event)
          })
        }

        devElement.addEventListener('click', (event) => {
          if (event.target.classList.contains('camera-btn')) {
            this.openCameraModal(device)
          } else {
            this.openDeviceModal(device)
          }
        })

        zoomContainer.appendChild(devElement)
      })

      // 绘制连接线
      this.drawConnections()
    },
    drawConnections() {
      const zoomContainer = this.$refs.zoomContainer
      if (!zoomContainer) return

      const svgNS = "http://www.w3.org/2000/svg"
      const svg = document.createElementNS(svgNS, 'svg')
      svg.setAttribute('width', '100%')
      svg.setAttribute('height', '100%')
      svg.style.position = 'absolute'
      svg.style.top = '0'
      svg.style.left = '0'
      svg.style.zIndex = '-1'

      this.devices.forEach(startDevice => {
        if (startDevice.connections) {
          startDevice.connections.forEach(endDeviceId => {
            const endDevice = this.devices.find(d => d.id === endDeviceId)
            if (!endDevice) return

            const startEl = document.getElementById(startDevice.id)
            const endEl = document.getElementById(endDevice.id)
            if (!startEl || !endEl) return

            const line = document.createElementNS(svgNS, 'path')
            const pathId = `path_${startDevice.id}_${endDevice.id}`
            line.setAttribute('id', pathId)

            const startX = startEl.offsetLeft + startEl.offsetWidth / 2
            const startY = startEl.offsetTop + startEl.offsetHeight / 2
            const endX = endEl.offsetLeft + endEl.offsetWidth / 2
            const endY = endEl.offsetTop + endEl.offsetHeight / 2

            let d = ''
            const isHorizontal = Math.abs(startY - endY) < 5
            const isVertical = Math.abs(startX - endX) < 5

            if (isHorizontal || isVertical) {
              d = `M ${startX} ${startY} L ${endX} ${endY}`
            } else {
              d = `M ${startX} ${startY} L ${startX} ${endY} L ${endX} ${endY}`
            }

            line.setAttribute('d', d)
            line.setAttribute('stroke', '#666')
            line.setAttribute('stroke-width', '2')
            line.setAttribute('fill', 'none')
            svg.appendChild(line)

            // 添加流动点
            const dot = document.createElement('div')
            dot.style.position = 'absolute'
            dot.style.width = '6px'
            dot.style.height = '6px'
            dot.style.backgroundColor = '#00ff00'
            dot.style.borderRadius = '50%'
            dot.style.offsetPath = `path('${d}')`
            dot.style.animation = `flow 4s linear infinite`
            dot.style.animationDelay = `${Math.random() * 4}s`
            zoomContainer.appendChild(dot)
          })
        }
      })

      zoomContainer.appendChild(svg)
    },
    updateZoom() {
      const layout = this.$refs.deviceLayout
      const zoomContainer = this.$refs.zoomContainer
      if (!layout || !zoomContainer) return

      const layoutWidth = layout.clientWidth
      const layoutHeight = layout.clientHeight

      let contentWidth = 0
      let contentHeight = 0
      this.devices.forEach(d => {
        if (d.x + 120 > contentWidth) contentWidth = d.x + 120
        if (d.y + 70 > contentHeight) contentHeight = d.y + 70
      })
      contentWidth += 100
      contentHeight += 50

      const scaleX = layoutWidth / contentWidth
      const scaleY = layoutHeight / contentHeight
      const scale = Math.min(scaleX, scaleY, 1)

      const offsetX = (layoutWidth - contentWidth * scale) / 2
      const offsetY = (layoutHeight - contentHeight * scale) / 2

      zoomContainer.style.width = `${contentWidth}px`
      zoomContainer.style.height = `${contentHeight}px`
      zoomContainer.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${scale})`
    },
    updateTooltipPosition(event) {
      this.tooltipPosition = {
        x: event.pageX + 15,
        y: event.pageY + 15
      }
    },
    openDeviceModal(device) {
      this.currentDevice = device
      this.modalTitle = `${device.name} - 设备详情`
      this.activeTab = device.status === 'alarm' ? 'alarm' : 'params'
      this.showModal = true
    },
    openCameraModal(device) {
      this.cameraTitle = `${device.name} - 实时监控`
      this.showCameraModal = true
    },
    closeModal() {
      this.showModal = false
      this.currentDevice = null
    },
    closeCameraModal() {
      this.showCameraModal = false
    },
    startPolling() {
      if (this.option.enablePolling !== false) {
        this.pollingTimer = setInterval(() => {
          this.updateDeviceStatuses()
        }, this.option.pollingInterval || 3000)
      }
    },
    async updateDeviceStatuses() {
      try {
        const newDevices = await this.getDeviceData()
        newDevices.forEach(newDevice => {
          const deviceElement = document.getElementById(newDevice.id)
          if (!deviceElement) return

          const currentDevice = this.devices.find(d => d.id === newDevice.id)
          if (currentDevice && currentDevice.status === newDevice.status) {
            return
          }

          // 更新状态
          deviceElement.classList.remove('running', 'idle', 'alarm')
          deviceElement.classList.add(newDevice.status)

          // 更新样式
          if (newDevice.status === 'running') {
            deviceElement.style.backgroundColor = '#4CAF50'
            deviceElement.style.borderColor = '#45a049'
            deviceElement.style.color = this.option.textColor || 'white'
          } else if (newDevice.status === 'idle') {
            deviceElement.style.backgroundColor = '#FFC107'
            deviceElement.style.borderColor = '#e0a800'
            deviceElement.style.color = this.option.textColor || 'black'
          } else if (newDevice.status === 'alarm') {
            deviceElement.style.backgroundColor = '#F44336'
            deviceElement.style.borderColor = '#da190b'
            deviceElement.style.color = this.option.textColor || 'white'
          }

          // 更新告警图标
          const alarmIcon = deviceElement.querySelector('.el-icon-warning')
          if (alarmIcon) {
            alarmIcon.style.display = newDevice.status === 'alarm' ? 'inline-block' : 'none'
          }

          // 更新内存中的设备数据
          if (currentDevice) {
            currentDevice.status = newDevice.status
          }
        })
      } catch (error) {
        console.error('Error updating device statuses:', error)
      }
    }
  }
}
</script>

<style scoped>
.equipment-monitoring-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

header {
  padding: 20px;
  text-align: center;
}

.equipment-header {
  padding: 20px;
  text-align: center;
}

.device-layout {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.zoom-container {
  position: relative;
  transform-origin: 0 0;
}

.device {
  position: absolute;
  transition: all 0.3s ease;
}

.device:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.modal {
  display: block;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
}

.modal-content {
  background-color: #fefefe;
  margin: 15% auto;
  padding: 20px;
  border: 1px solid #888;
  border-radius: 8px;
  width: 80%;
  max-width: 600px;
  color: #333;
}

.camera-view {
  max-width: 900px;
}

.close-button {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close-button:hover {
  color: black;
}

.tabs {
  display: flex;
  margin-bottom: 20px;
}

.tab-link {
  background-color: #f1f1f1;
  border: none;
  padding: 10px 20px;
  cursor: pointer;
  margin-right: 5px;
  border-radius: 4px 4px 0 0;
}

.tab-link.active {
  background-color: #007bff;
  color: white;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.device-tooltip {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  pointer-events: none;
  z-index: 1000;
  font-size: 12px;
}

@keyframes flow {
  0% {
    offset-distance: 0%;
  }
  100% {
    offset-distance: 100%;
  }
}
</style>
