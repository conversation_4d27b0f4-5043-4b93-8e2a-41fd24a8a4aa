# 设备监控台插件使用指南

## 📋 概述

设备监控台插件是一个专为工业设备实时监控设计的Vue.js组件，可以直接拖拽到大屏画布上使用。该插件支持设备状态可视化、连接关系展示、实时数据更新等功能。

## 🚀 主要功能

- ✅ **实时设备状态监控** - 支持运行中、空闲、报警三种状态
- ✅ **设备连接关系可视化** - 显示设备间的连接线和流动动画
- ✅ **设备详情弹窗** - 包含关键参数、保养信息、报警详情
- ✅ **摄像头监控集成** - 支持设备摄像头实时画面查看
- ✅ **鼠标悬停提示** - 显示设备基本信息
- ✅ **自适应缩放** - 根据屏幕大小自动调整布局
- ✅ **轮询更新** - 可配置的数据自动刷新
- ✅ **自定义设备数据** - 支持手动添加和配置设备

## 📦 安装使用

### 1. 添加到组件面板

插件已自动添加到左侧组件面板的"二次开发"分类中，名称为"设备监控台"。

### 2. 拖拽使用

1. 从左侧组件面板找到"设备监控台"组件
2. 拖拽到画布上合适的位置
3. 调整组件大小（推荐尺寸：1200x800）
4. 在右侧配置面板中设置组件属性

## ⚙️ 配置选项

### 基础配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | String | "产线设备实时监控" | 组件标题 |
| showHeader | Boolean | true | 是否显示标题栏 |
| backgroundColor | String | "#1a1a1a" | 背景颜色 |
| textColor | String | "#ffffff" | 文字颜色 |
| headerFontSize | Number | 24 | 标题字体大小 |
| headerColor | String | "#ffffff" | 标题颜色 |
| headerAlign | String | "center" | 标题对齐方式 |

### 数据配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| enablePolling | Boolean | true | 是否启用轮询更新 |
| pollingInterval | Number | 3000 | 轮询间隔（毫秒） |
| customDevices | Array | [] | 自定义设备数据 |

## 🔧 设备数据格式

### 设备对象结构

```json
{
  "id": "dev-01",                    // 设备唯一标识
  "name": "放板机",                   // 设备显示名称
  "x": 50,                          // X坐标位置
  "y": 50,                          // Y坐标位置
  "status": "running",              // 设备状态：running/idle/alarm
  "connections": ["dev-02"],        // 连接的设备ID数组
  "icons": {                        // 图标配置
    "camera": true,                 // 是否显示摄像头图标
    "star": false                   // 是否显示星标图标
  },
  "tooltipData": {                  // 鼠标悬停提示信息
    "型号": "LDR-2000",
    "厂商": "A-Tech",
    "投入日期": "2022-08-15"
  }
}
```

### 设备状态说明

- **running** (运行中) - 绿色背景，设备正常运行
- **idle** (空闲) - 黄色背景，设备空闲待机
- **alarm** (报警) - 红色背景，设备故障报警

## 📝 使用示例

### 1. 基础配置示例

```json
{
  "title": "生产线设备监控",
  "showHeader": true,
  "backgroundColor": "#1a1a1a",
  "enablePolling": true,
  "pollingInterval": 5000,
  "customDevices": [
    {
      "id": "dev-01",
      "name": "放板机",
      "x": 50,
      "y": 50,
      "status": "running",
      "connections": ["dev-02"],
      "tooltipData": {
        "型号": "LDR-2000",
        "厂商": "A-Tech"
      }
    },
    {
      "id": "dev-02",
      "name": "激光打码机",
      "x": 200,
      "y": 50,
      "status": "running",
      "connections": ["dev-03"],
      "icons": {
        "camera": true
      },
      "tooltipData": {
        "型号": "LM-500",
        "功率": "50W"
      }
    }
  ]
}
```

### 2. 添加设备步骤

1. 在配置面板中点击"添加设备"按钮
2. 填写设备基本信息：
   - 设备ID（必填，唯一标识）
   - 设备名称（必填，显示名称）
   - X、Y坐标（设备在画布上的位置）
   - 设备状态（运行中/空闲/报警）
3. 配置连接关系：选择当前设备连接的其他设备
4. 设置图标：选择是否显示摄像头或星标图标
5. 编辑提示信息：添加设备的详细属性信息
6. 点击保存

### 3. 批量编辑设备

1. 点击"批量编辑"按钮
2. 在文本框中编辑JSON格式的设备数据
3. 可以使用"加载默认数据"快速获取示例数据
4. 支持"从JSON导入"和"导出为JSON"功能
5. 编辑完成后点击保存

## 🎯 最佳实践

### 1. 设备布局建议

- 设备间距保持在150-200px，确保连接线清晰可见
- 重要设备可以放在显眼位置
- 考虑生产流程的逻辑顺序进行布局

### 2. 状态配色建议

- 保持默认的状态颜色配置，符合工业标准
- 绿色表示正常，黄色表示注意，红色表示警告

### 3. 数据更新频率

- 根据实际需求设置轮询间隔
- 关键设备可以设置较短的更新间隔（1-3秒）
- 非关键设备可以设置较长的更新间隔（5-10秒）

## 🔗 集成数据源

### 1. 静态数据

直接在配置面板中添加设备数据，适用于设备配置相对固定的场景。

### 2. API数据源

可以通过组件的数据源配置，连接到实际的设备监控API，实现真实的数据展示。

### 3. 全局数据源

支持使用平台的全局数据源功能，实现多个组件共享设备数据。

## 🐛 常见问题

### Q: 设备不显示怎么办？
A: 检查设备的x、y坐标是否在可视范围内，确保设备ID唯一且不为空。

### Q: 连接线不显示？
A: 确保connections数组中的设备ID在设备列表中存在，检查设备位置是否合理。

### Q: 轮询不工作？
A: 确认enablePolling设置为true，检查pollingInterval是否为有效数值。

### Q: 弹窗不显示？
A: 确保点击的是设备区域而不是连接线，检查浏览器控制台是否有错误信息。

## 📞 技术支持

如有问题或建议，请联系开发团队或查看项目文档。

---

**版本**: v1.0.0  
**更新时间**: 2024-01-17  
**兼容性**: Vue 2.x + Element UI
