<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>综合格式化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-section h2 {
            color: #555;
            margin-top: 0;
        }
        .editor-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .editor-panel {
            border: 1px solid #ccc;
            border-radius: 4px;
            background: white;
        }
        .editor-header {
            background: #f8f9fa;
            padding: 10px;
            border-bottom: 1px solid #ccc;
            font-weight: bold;
        }
        .editor {
            width: 100%;
            height: 300px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            padding: 10px;
            border: none;
            resize: vertical;
            background: #1e1e1e;
            color: #d4d4d4;
        }
        .button-group {
            margin: 10px 0;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #409eff;
            color: white;
        }
        .btn-primary:hover {
            background: #66b1ff;
        }
        .btn-success {
            background: #67c23a;
            color: white;
        }
        .btn-success:hover {
            background: #85ce61;
        }
        .btn-warning {
            background: #e6a23c;
            color: white;
        }
        .btn-warning:hover {
            background: #ebb563;
        }
        .btn-danger {
            background: #f56c6c;
            color: white;
        }
        .btn-danger:hover {
            background: #f78989;
        }
        .btn:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #e1f3d8;
            color: #67c23a;
            border: 1px solid #b3d8a4;
        }
        .status.warning {
            background: #fdf6ec;
            color: #e6a23c;
            border: 1px solid #f0c78a;
        }
        .status.error {
            background: #fef0f0;
            color: #f56c6c;
            border: 1px solid #f5a9a9;
        }
        .status.info {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
            border-bottom: 1px solid #eee;
        }
        .log-entry:last-child {
            border-bottom: none;
        }
        .timestamp {
            color: #666;
            font-size: 11px;
        }
        .counter {
            background: #909399;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 10px;
        }
        .test-results {
            margin: 20px 0;
        }
        .test-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .test-item.pass {
            background: #e1f3d8;
            border-left-color: #67c23a;
        }
        .test-item.fail {
            background: #fef0f0;
            border-left-color: #f56c6c;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-x: auto;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 综合格式化测试</h1>
        <p>测试修复后的格式化功能，包括重复点击保护和注释处理修复</p>

        <div class="test-section">
            <h2>实时格式化测试</h2>
            <div class="editor-container">
                <div class="editor-panel">
                    <div class="editor-header">输入代码</div>
                    <textarea id="inputEditor" class="editor" placeholder="在这里输入JavaScript代码...">(data, params, refs) => {
    function sortByValueAndKey(arr) {
        return [...arr].sort((a, b) => {
            // 先按 value 排序  const valueCompare = a.TEMPLATE_CODE.localeCompare(b.TEMPLATE_CODE);
            if (valueCompare !== 0) return valueCompare;
            // 如果 value 相同，则按 key 排序  return a.EQUIPMENT_NAME - b.EQUIPMENT_NAME;
        });
    }
    return sortByValueAndKey(data)
}</textarea>
                </div>
                <div class="editor-panel">
                    <div class="editor-header">格式化结果</div>
                    <textarea id="outputEditor" class="editor" readonly placeholder="格式化后的代码将显示在这里..."></textarea>
                </div>
            </div>
            
            <div class="button-group">
                <button id="formatOnce" class="btn btn-success">格式化一次</button>
                <button id="formatTwice" class="btn btn-warning">格式化两次</button>
                <button id="formatThrice" class="btn btn-danger">格式化三次</button>
                <button id="rapidFormat" class="btn btn-primary">快速连击测试</button>
                <button id="resetCode" class="btn">重置代码</button>
                <span id="formatCounter" class="counter">格式化次数: 0</span>
            </div>
            
            <div id="formatStatus" class="status info">准备就绪</div>
            <div id="formatLog" class="log"></div>
        </div>

        <div class="test-section">
            <h2>自动化测试结果</h2>
            <button id="runAutoTests" class="btn btn-primary">运行自动化测试</button>
            <button id="clearTestResults" class="btn">清空结果</button>
            <div id="testResults" class="test-results"></div>
        </div>

        <div class="test-section">
            <h2>修复总结</h2>
            <div class="comparison">
                <div>
                    <h3>🔧 修复的问题</h3>
                    <ul>
                        <li><strong>重复点击保护</strong>：防止多个格式化操作同时进行</li>
                        <li><strong>注释处理修复</strong>：防止注释吞噬后续代码</li>
                        <li><strong>状态管理</strong>：正确的异步操作状态管理</li>
                        <li><strong>用户体验</strong>：友好的提示和视觉反馈</li>
                    </ul>
                </div>
                <div>
                    <h3>✅ 验证要点</h3>
                    <ul>
                        <li><strong>稳定性</strong>：重复格式化结果一致</li>
                        <li><strong>完整性</strong>：代码不丢失、不损坏</li>
                        <li><strong>注释安全</strong>：注释不会吞噬代码</li>
                        <li><strong>操作符正确</strong>：操作符不被错误分割</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 引入修复后的格式化方法
        // 安全的注释保护方法
        function safeCommentProtection(code, commentPlaceholders, startIndex) {
            let result = '';
            let i = 0;
            let commentIndex = startIndex;
            
            while (i < code.length) {
                // 检查多行注释
                if (i < code.length - 1 && code[i] === '/' && code[i + 1] === '*') {
                    let commentStart = i;
                    i += 2;
                    
                    while (i < code.length - 1) {
                        if (code[i] === '*' && code[i + 1] === '/') {
                            i += 2;
                            break;
                        }
                        i++;
                    }
                    
                    const comment = code.substring(commentStart, i);
                    const placeholder = `__COMMENT_${commentIndex++}__`;
                    commentPlaceholders.push({ placeholder, content: comment });
                    result += placeholder;
                    continue;
                }
                
                // 检查单行注释
                if (i < code.length - 1 && code[i] === '/' && code[i + 1] === '/') {
                    let commentStart = i;
                    i += 2;
                    
                    while (i < code.length) {
                        const char = code[i];
                        if (char === '\n' || char === '\r' || 
                            char === ';' || 
                            (char === ' ' && i + 1 < code.length && /[a-zA-Z_$]/.test(code[i + 1]))) {
                            break;
                        }
                        i++;
                    }
                    
                    const comment = code.substring(commentStart, i);
                    const placeholder = `__COMMENT_${commentIndex++}__`;
                    commentPlaceholders.push({ placeholder, content: comment });
                    result += placeholder;
                    continue;
                }
                
                result += code[i];
                i++;
            }
            
            return result;
        }

        // 修复后的格式化方法
        function formatJavaScriptFixed(code) {
            if (!code || code.trim() === '') return code;
            
            try {
                // 1. 保护字符串
                const strings = [];
                let stringIndex = 0;
                let formatted = code.replace(/(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g, (match) => {
                    const placeholder = `__STR_${stringIndex++}__`;
                    strings.push({ placeholder, content: match });
                    return placeholder;
                });

                // 2. 安全的注释保护
                const comments = [];
                let commentIndex = 0;
                formatted = safeCommentProtection(formatted, comments, commentIndex);

                // 3. 保护箭头函数
                const arrows = [];
                let arrowIndex = 0;
                formatted = formatted.replace(/\s*=>\s*/g, () => {
                    const placeholder = `__ARROW_${arrowIndex++}__`;
                    arrows.push({ placeholder, content: ' => ' });
                    return placeholder;
                });

                // 4. 基本空白处理
                formatted = formatted.replace(/\s+/g, ' ').trim();

                // 5. 处理操作符
                formatted = formatted
                    .replace(/([^=!<>])===([^=])/g, '$1 === $2')
                    .replace(/([^=!])!==([^=])/g, '$1 !== $2')
                    .replace(/([^=!<>])<=([^=])/g, '$1 <= $2')
                    .replace(/([^=!<>])>=([^=])/g, '$1 >= $2')
                    .replace(/([^=!<>])==([^=])/g, '$1 == $2')
                    .replace(/([^=!])!=([^=])/g, '$1 != $2')
                    .replace(/([^=!<>])<([^=])/g, '$1 < $2')
                    .replace(/([^=!<>])>([^=])/g, '$1 > $2')
                    .replace(/([^&])&&([^&])/g, '$1 && $2')
                    .replace(/([^|])\|\|([^|])/g, '$1 || $2')
                    .replace(/([^+])\+([^+=])/g, '$1 + $2')
                    .replace(/([^-])-([^-=])/g, '$1 - $2')
                    .replace(/([^*])\*([^*=])/g, '$1 * $2')
                    .replace(/([^%])%([^=])/g, '$1 % $2')
                    .replace(/([a-zA-Z_$][a-zA-Z0-9_$]*|\]|\))=([^=])/g, '$1 = $2');

                // 6. 处理标点
                formatted = formatted
                    .replace(/,(?!\s)/g, ', ')
                    .replace(/;(?!\s*\n)/g, ';\n')
                    .replace(/\{(?!\s*\n)/g, ' {\n')
                    .replace(/\}(?!\s*\n)/g, '\n}')
                    .replace(/\s*\(\s*/g, '(')
                    .replace(/\s*\)\s*/g, ')');

                // 7. 最终清理
                formatted = formatted
                    .replace(/\s*{\s*/g, ' {\n')
                    .replace(/\s*}\s*/g, '\n}')
                    .replace(/\s*;\s*/g, ';\n')
                    .replace(/\s*,\s*/g, ', ');

                // 8. 恢复箭头函数
                arrows.forEach(({ placeholder, content }) => {
                    formatted = formatted.replace(placeholder, content);
                });

                // 9. 恢复注释
                comments.forEach(({ placeholder, content }) => {
                    formatted = formatted.replace(placeholder, content);
                });

                // 10. 恢复字符串
                strings.forEach(({ placeholder, content }) => {
                    formatted = formatted.replace(placeholder, content);
                });

                // 11. 缩进
                const lines = formatted.split('\n');
                let indent = 0;
                const result = lines.map(line => {
                    line = line.trim();
                    if (!line) return '';
                    if (line.includes('}')) indent = Math.max(0, indent - 1);
                    const indented = '    '.repeat(indent) + line;
                    if (line.includes('{')) indent++;
                    return indented;
                }).join('\n');

                return result;
            } catch (error) {
                console.error('格式化失败:', error);
                return code;
            }
        }

        // 页面交互逻辑
        let formatCount = 0;
        let isFormatting = false;

        // DOM元素
        const inputEditor = document.getElementById('inputEditor');
        const outputEditor = document.getElementById('outputEditor');
        const formatStatus = document.getElementById('formatStatus');
        const formatLog = document.getElementById('formatLog');
        const formatCounter = document.getElementById('formatCounter');

        // 日志记录
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            formatLog.appendChild(entry);
            formatLog.scrollTop = formatLog.scrollHeight;
        }

        // 更新状态
        function updateStatus(message, type = 'info') {
            formatStatus.textContent = message;
            formatStatus.className = `status ${type}`;
        }

        // 更新计数器
        function updateCounter() {
            formatCounter.textContent = `格式化次数: ${formatCount}`;
        }

        // 格式化函数（带防重复保护）
        async function formatCode(times = 1) {
            if (isFormatting) {
                log('格式化正在进行中，忽略重复请求', 'warning');
                return;
            }

            isFormatting = true;
            updateStatus('正在格式化...', 'warning');

            try {
                let code = inputEditor.value;

                for (let i = 0; i < times; i++) {
                    formatCount++;
                    log(`开始第 ${formatCount} 次格式化`);

                    // 模拟异步延迟
                    await new Promise(resolve => setTimeout(resolve, 100));

                    code = formatJavaScriptFixed(code);
                    log(`完成第 ${formatCount} 次格式化`);
                }

                outputEditor.value = code;
                updateCounter();
                updateStatus(`格式化完成 (${times}次)`, 'success');

            } catch (error) {
                log(`格式化失败: ${error.message}`, 'error');
                updateStatus(`格式化失败: ${error.message}`, 'error');
            } finally {
                isFormatting = false;
            }
        }

        // 快速连击测试
        async function rapidFormatTest() {
            log('开始快速连击测试...', 'info');

            for (let i = 0; i < 5; i++) {
                formatCode(1);
                await new Promise(resolve => setTimeout(resolve, 50));
            }

            log('快速连击测试完成', 'info');
        }

        // 重置代码
        function resetCode() {
            inputEditor.value = `(data, params, refs) => {
    function sortByValueAndKey(arr) {
        return [...arr].sort((a, b) => {
            // 先按 value 排序  const valueCompare = a.TEMPLATE_CODE.localeCompare(b.TEMPLATE_CODE);
            if (valueCompare !== 0) return valueCompare;
            // 如果 value 相同，则按 key 排序  return a.EQUIPMENT_NAME - b.EQUIPMENT_NAME;
        });
    }
    return sortByValueAndKey(data)
}`;
            outputEditor.value = '';
            formatCount = 0;
            updateCounter();
            updateStatus('代码已重置', 'info');
            log('代码已重置');
        }

        // 自动化测试
        function runAutomatedTests() {
            const testCases = [
                {
                    name: '用户反馈的原始问题',
                    code: `(data, params, refs) => {
    function sortByValueAndKey(arr) {
        return [...arr].sort((a, b) => {
            // 先按 value 排序  const valueCompare = a.TEMPLATE_CODE.localeCompare(b.TEMPLATE_CODE);
            if (valueCompare !== 0) return valueCompare;
            // 如果 value 相同，则按 key 排序  return a.EQUIPMENT_NAME - b.EQUIPMENT_NAME;
        });
    }
    return sortByValueAndKey(data)
}`
                },
                {
                    name: '复杂注释混合',
                    code: `// 函数开始  function test() { /* 内部注释 */ return true; // 返回值  }`
                },
                {
                    name: '操作符测试',
                    code: `if(a!==b&&c<=d||e>=f){return x===y}`
                }
            ];

            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '';

            let passCount = 0;

            testCases.forEach((testCase, index) => {
                const original = testCase.code;

                // 连续格式化3次
                const first = formatJavaScriptFixed(original);
                const second = formatJavaScriptFixed(first);
                const third = formatJavaScriptFixed(second);

                // 检查稳定性
                const isStable = second === third;

                // 检查代码完整性
                const hasCodeLoss = (original.includes('const') && !second.includes('const')) ||
                                   (original.includes('return') && !second.includes('return')) ||
                                   (original.includes('if') && !second.includes('if'));

                // 检查注释安全
                const commentCount = (original.match(/\/\//g) || []).length;
                const formattedCommentCount = (second.match(/\/\//g) || []).length;
                const commentSafe = commentCount === formattedCommentCount;

                const passed = isStable && !hasCodeLoss && commentSafe;
                if (passed) passCount++;

                const testDiv = document.createElement('div');
                testDiv.className = `test-item ${passed ? 'pass' : 'fail'}`;
                testDiv.innerHTML = `
                    <h4>${passed ? '✅' : '❌'} 测试 ${index + 1}: ${testCase.name}</h4>
                    <div><strong>检查结果:</strong></div>
                    <div>
                        稳定性: ${isStable ? '✅' : '❌'} (第2次和第3次格式化结果${isStable ? '相同' : '不同'})<br>
                        代码完整性: ${hasCodeLoss ? '❌ 有代码丢失' : '✅ 完整'}<br>
                        注释安全: ${commentSafe ? '✅ 安全' : '❌ 注释数量不匹配'}<br>
                        <strong>总体: ${passed ? '✅ 通过' : '❌ 失败'}</strong>
                    </div>
                    <details>
                        <summary>查看详细结果</summary>
                        <div class="code-block">${second}</div>
                    </details>
                `;
                resultsDiv.appendChild(testDiv);
            });

            // 总结
            const summary = document.createElement('div');
            summary.className = `test-item ${passCount === testCases.length ? 'pass' : 'fail'}`;
            summary.innerHTML = `
                <h3>${passCount === testCases.length ? '🎉' : '⚠️'} 自动化测试总结</h3>
                <div>通过: ${passCount}/${testCases.length}</div>
                <div>状态: ${passCount === testCases.length ? '✅ 所有测试通过，修复成功' : '❌ 仍有问题需要修复'}</div>
            `;
            resultsDiv.insertBefore(summary, resultsDiv.firstChild);
        }

        // 事件绑定
        document.getElementById('formatOnce').addEventListener('click', () => formatCode(1));
        document.getElementById('formatTwice').addEventListener('click', () => formatCode(2));
        document.getElementById('formatThrice').addEventListener('click', () => formatCode(3));
        document.getElementById('rapidFormat').addEventListener('click', rapidFormatTest);
        document.getElementById('resetCode').addEventListener('click', resetCode);
        document.getElementById('runAutoTests').addEventListener('click', runAutomatedTests);
        document.getElementById('clearTestResults').addEventListener('click', () => {
            document.getElementById('testResults').innerHTML = '';
        });

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，准备进行综合测试');
            updateStatus('准备就绪', 'info');
            updateCounter();
        });
    </script>
</body>
</html>
