document.addEventListener('DOMContentLoaded', () => {
    const layout = document.getElementById('device-layout');
    const zoomContainer = document.getElementById('zoom-container');
    let devices = []; // Will be populated from API

    // Create a single tooltip element for the page
    const tooltip = document.createElement('div');
    tooltip.id = 'device-tooltip';
    tooltip.style.position = 'absolute';
    tooltip.style.display = 'none';
    tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    tooltip.style.color = 'white';
    tooltip.style.padding = '8px 12px';
    tooltip.style.borderRadius = '4px';
    tooltip.style.pointerEvents = 'none'; // Prevent tooltip from blocking mouse events
    tooltip.style.zIndex = '1000'; // Ensure it's on top
    tooltip.style.fontSize = '12px';
    document.body.appendChild(tooltip);

    function initializeLayout(fetchedDevices) {
        devices = fetchedDevices;

        // Clear any existing elements in case of re-initialization
        zoomContainer.innerHTML = '';

        // 自适应缩放逻辑
        function updateZoom() {
            const layoutWidth = layout.clientWidth;
            const layoutHeight = layout.clientHeight;

            let contentWidth = 0;
            let contentHeight = 0;
            devices.forEach(d => {
                if (d.x + 120 > contentWidth) contentWidth = d.x + 120; // 120 is device width
                if (d.y + 70 > contentHeight) contentHeight = d.y + 70; // 70 is device height
            });
            contentWidth += 100; // Add more horizontal padding (50 left, 50 right)
            contentHeight += 50; // Add vertical padding

            const scaleX = layoutWidth / contentWidth;
            const scaleY = layoutHeight / contentHeight;
            const scale = Math.min(scaleX, scaleY, 1);

            const offsetX = (layoutWidth - contentWidth * scale) / 2;
            const offsetY = (layoutHeight - contentHeight * scale) / 2;

            zoomContainer.style.width = `${contentWidth}px`;
            zoomContainer.style.height = `${contentHeight}px`;
            zoomContainer.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${scale})`;
        }

        // 渲染设备
        devices.forEach(device => {
            const devElement = document.createElement('div');
            devElement.className = `device ${device.status}`;
            devElement.id = device.id;
            devElement.style.left = `${device.x}px`;
            devElement.style.top = `${device.y}px`;

            const name = document.createElement('div');
            name.className = 'device-name';
            name.textContent = device.name;

            const icons = document.createElement('div');
            icons.className = 'device-icons';
            let iconHtml = '<i class="fa-solid fa-triangle-exclamation" style="display: none;"></i>';
            if (device.icons?.camera) {
                iconHtml += '<i class="fa-solid fa-video camera-btn"></i>';
            }
            if (device.icons?.star) {
                iconHtml += '<i class="fa-solid fa-star"></i>';
            }
            icons.innerHTML = iconHtml;

            devElement.appendChild(name);
            devElement.appendChild(icons);

            // Add tooltip event listeners
            if (device.tooltipData) {
                devElement.addEventListener('mouseover', (event) => {
                    let content = '';
                    for (const [key, value] of Object.entries(device.tooltipData)) {
                        content += `<div><strong>${key}:</strong> ${value}</div>`;
                    }
                    tooltip.innerHTML = content;
                    tooltip.style.display = 'block';
                });

                devElement.addEventListener('mouseout', () => {
                    tooltip.style.display = 'none';
                });

                devElement.addEventListener('mousemove', (event) => {
                    // Position tooltip near the cursor
                    tooltip.style.left = `${event.pageX + 15}px`;
                    tooltip.style.top = `${event.pageY + 15}px`;
                });
            }

            zoomContainer.appendChild(devElement);
        });

        // 绘制连接线和流动点
        const svgNS = "http://www.w3.org/2000/svg";
        const svg = document.createElementNS(svgNS, 'svg');
        svg.setAttribute('width', '100%');
        svg.setAttribute('height', '100%');
        svg.style.position = 'absolute';
        svg.style.top = 0;
        svg.style.left = 0;
        svg.style.zIndex = -1;

        devices.forEach(startDevice => {
            if (startDevice.connections) {
                startDevice.connections.forEach(endDeviceId => {
                    const endDevice = devices.find(d => d.id === endDeviceId);
                    if (!endDevice) return;

                    const startEl = document.getElementById(startDevice.id);
                    const endEl = document.getElementById(endDevice.id);

                    const line = document.createElementNS(svgNS, 'path');
                    const pathId = `path_${startDevice.id}_${endDevice.id}`;
                    line.setAttribute('id', pathId);

                    const startX = startEl.offsetLeft + startEl.offsetWidth / 2;
                    const startY = startEl.offsetTop + startEl.offsetHeight / 2;
                    const endX = endEl.offsetLeft + endEl.offsetWidth / 2;
                    const endY = endEl.offsetTop + endEl.offsetHeight / 2;

                    let d = '';
                    const isHorizontal = Math.abs(startY - endY) < 5;
                    const isVertical = Math.abs(startX - endX) < 5;

                    if (isHorizontal || isVertical) {
                        d = `M ${startX} ${startY} L ${endX} ${endY}`;
                    } else {
                        d = `M ${startX} ${startY} L ${startX} ${endY} L ${endX} ${endY}`;
                    }

                    line.setAttribute('d', d);
                    line.setAttribute('class', 'connector');
                    svg.appendChild(line);

                    const dot = document.createElement('div');
                    dot.className = 'flow-dot';
                    dot.style.offsetPath = `path('${d}')`;
                    dot.style.animationDelay = `${Math.random() * 4}s`;
                    zoomContainer.appendChild(dot);
                });
            }
        });
        zoomContainer.appendChild(svg);

        // 弹窗逻辑
        const modal = document.getElementById('modal');
        const cameraModal = document.getElementById('camera-modal');
        const closeModalButtons = document.querySelectorAll('.close-button');
        const tabs = document.querySelectorAll('.tab-link');
        const tabContents = document.querySelectorAll('.tab-content');

        function openModal(modalElement) {
            modalElement.style.display = 'block';
        }

        function closeModal(modalElement) {
            modalElement.style.display = 'none';
        }

        closeModalButtons.forEach(button => {
            button.addEventListener('click', () => {
                closeModal(modal);
                closeModal(cameraModal);
            });
        });

        window.addEventListener('click', (event) => {
            if (event.target == modal || event.target == cameraModal) {
                closeModal(modal);
                closeModal(cameraModal);
            }
        });

        zoomContainer.addEventListener('click', (event) => {
            const deviceElement = event.target.closest('.device');
            if (!deviceElement) return;

            const deviceData = devices.find(d => d.id === deviceElement.id);
            if (!deviceData) return;

            if (event.target.classList.contains('camera-btn')) {
                document.getElementById('camera-title').textContent = `${deviceData.name} - 实时监控`;
                openModal(cameraModal);
            } else {
                document.getElementById('modal-title').textContent = `${deviceData.name} - 设备详情`;
                document.getElementById('param-temp').textContent = (Math.random() * 20 + 60).toFixed(1);
                document.getElementById('param-pressure').textContent = (Math.random() * 50 + 100).toFixed(1);
                document.getElementById('param-speed').textContent = (Math.random() * 10 + 5).toFixed(1);
                
                if (deviceElement.classList.contains('alarm')) {
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    document.querySelector('[data-tab="alarm"]').classList.add('active');
                    document.getElementById('alarm').classList.add('active');
                }

                openModal(modal);
            }
        });

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                tab.classList.add('active');
                document.getElementById(tab.dataset.tab).classList.add('active');
            });
        });

        // 初始加载和窗口大小变化时更新缩放
        updateZoom();
        window.addEventListener('resize', updateZoom);

    }

    // 平滑更新设备状态，而不是重新渲染整个布局
    function updateDeviceStatuses(newDevices) {
        newDevices.forEach(newDevice => {
            const deviceElement = document.getElementById(newDevice.id);
            if (!deviceElement) return; // 如果设备元素不存在，则跳过

            // 查找当前设备数据
            const currentDevice = devices.find(d => d.id === newDevice.id);
            if (currentDevice && currentDevice.status === newDevice.status) {
                return; // 状态未改变，无需更新
            }

            // 更新状态
            deviceElement.classList.remove('running', 'idle', 'alarm');
            deviceElement.classList.add(newDevice.status);

            // 更新告警图标
            const alarmIcon = deviceElement.querySelector('.fa-triangle-exclamation');
            if (alarmIcon) {
                alarmIcon.style.display = newDevice.status === 'alarm' ? 'inline-block' : 'none';
            }

            // 更新内存中的设备数据
            if(currentDevice) {
                currentDevice.status = newDevice.status;
            }
        });
    }

    // 定时从服务器获取最新数据并更新
    function pollForUpdates() {
        getDeviceData().then(newDeviceData => {
            updateDeviceStatuses(newDeviceData);
        }).catch(error => {
            console.error('Error polling for updates:', error);
        });
    }

    // 初始加载
    getDeviceData().then(initialDeviceData => {
        initializeLayout(initialDeviceData);
        // 初始加载成功后，开始轮询更新
        setInterval(pollForUpdates, 3000); // 每3秒更新一次
    }).catch(error => {
        console.error('Failed to load initial device data:', error);
    });
});


