<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作符专项测试</title>
    <style>
        body {
            font-family: monospace;
            margin: 20px;
            background: #000;
            color: #0f0;
        }
        .test {
            border: 2px solid;
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .pass { border-color: #0f0; background: rgba(0, 255, 0, 0.1); }
        .fail { border-color: #f00; background: rgba(255, 0, 0, 0.1); color: #f00; }
        .code {
            background: #222;
            padding: 5px;
            margin: 5px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔍 操作符专项测试</h1>
    <p>专门测试各种操作符的格式化问题</p>

    <div id="results"></div>

    <script>
        // 修复后的格式化函数
        function formatJavaScript(code) {
            if (!code || code.trim() === '') return code;

            try {
                // 1. 保护字符串
                const strings = [];
                let stringIndex = 0;
                let formatted = code.replace(/(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g, (match) => {
                    const placeholder = `__STR_${stringIndex++}__`;
                    strings.push({ placeholder, content: match });
                    return placeholder;
                });

                // 2. 保护注释
                const comments = [];
                let commentIndex = 0;
                formatted = formatted.replace(/\/\/.*$/gm, (match) => {
                    const placeholder = `__COMMENT_${commentIndex++}__`;
                    comments.push({ placeholder, content: match });
                    return placeholder;
                });
                formatted = formatted.replace(/\/\*[\s\S]*?\*\//g, (match) => {
                    const placeholder = `__COMMENT_${commentIndex++}__`;
                    comments.push({ placeholder, content: match });
                    return placeholder;
                });

                // 3. 保护箭头函数
                const arrows = [];
                let arrowIndex = 0;
                formatted = formatted.replace(/\s*=>\s*/g, () => {
                    const placeholder = `__ARROW_${arrowIndex++}__`;
                    arrows.push({ placeholder, content: ' => ' });
                    return placeholder;
                });

                // 4. 基本空白处理
                formatted = formatted.replace(/\s+/g, ' ').trim();

                // 5. 处理操作符（使用负向前瞻避免冲突）
                formatted = formatted
                    // 先处理长的操作符
                    .replace(/\s*(===)\s*/g, ' $1 ')  // 严格相等
                    .replace(/\s*(!==)\s*/g, ' $1 ')  // 严格不等
                    .replace(/\s*(<=)\s*/g, ' $1 ')   // 小于等于
                    .replace(/\s*(>=)\s*/g, ' $1 ')   // 大于等于
                    .replace(/\s*(==)\s*/g, ' $1 ')   // 相等
                    .replace(/\s*(!=)\s*/g, ' $1 ')   // 不等
                    .replace(/\s*(<)(?!=)\s*/g, ' $1 ')    // 小于（不是<=）
                    .replace(/\s*(>)(?!=)\s*/g, ' $1 ')    // 大于（不是>=）
                    .replace(/\s*(&&)\s*/g, ' $1 ')   // 逻辑与
                    .replace(/\s*(\|\|)\s*/g, ' $1 ') // 逻辑或
                    .replace(/\s*([+\-])\s*/g, ' $1 ')     // 加减
                    .replace(/\s*([*%])\s*/g, ' $1 ')      // 乘模
                    .replace(/\s*([a-zA-Z_$][a-zA-Z0-9_$]*|\]|\))\s*=\s*([^=])/g, '$1 = $2');

                // 6. 恢复所有内容
                arrows.forEach(({ placeholder, content }) => {
                    formatted = formatted.replace(placeholder, content);
                });
                comments.forEach(({ placeholder, content }) => {
                    formatted = formatted.replace(placeholder, content);
                });
                strings.forEach(({ placeholder, content }) => {
                    formatted = formatted.replace(placeholder, content);
                });

                return formatted;

            } catch (error) {
                console.error('格式化错误:', error);
                return code;
            }
        }

        // 检查特定问题
        function checkOperatorIssues(code) {
            const issues = [];
            
            if (code.includes('!= =')) issues.push('!==被分割为!= =');
            if (code.includes('= = =')) issues.push('===被分割为= = =');
            if (code.includes('< =')) issues.push('<=被分割为< =');
            if (code.includes('> =')) issues.push('>=被分割为> =');
            if (code.includes('& &')) issues.push('&&被分割为& &');
            if (code.includes('| |')) issues.push('||被分割为| |');
            if (code.includes('/ /')) issues.push('//被分割为/ /');
            if (code.includes('= >')) issues.push('=>被分割为= >');
            
            return issues;
        }

        // 测试用例
        const operatorTests = [
            { name: '严格相等', code: 'a===b', expected: 'a === b' },
            { name: '严格不等', code: 'a!==b', expected: 'a !== b' },
            { name: '小于等于', code: 'a<=b', expected: 'a <= b' },
            { name: '大于等于', code: 'a>=b', expected: 'a >= b' },
            { name: '相等', code: 'a==b', expected: 'a == b' },
            { name: '不等', code: 'a!=b', expected: 'a != b' },
            { name: '小于', code: 'a<b', expected: 'a < b' },
            { name: '大于', code: 'a>b', expected: 'a > b' },
            { name: '逻辑与', code: 'a&&b', expected: 'a && b' },
            { name: '逻辑或', code: 'a||b', expected: 'a || b' },
            { name: '箭头函数', code: 'a=>b', expected: 'a => b' },
            { name: '注释', code: '//comment', expected: '//comment' },
            { name: '复合测试', code: 'if(a!==b&&c>=d||e<=f)', expected: 'if(a !== b && c >= d || e <= f)' }
        ];

        // 运行测试
        function runOperatorTests() {
            const results = document.getElementById('results');
            let passCount = 0;

            operatorTests.forEach((test, index) => {
                console.log(`\n测试 ${index + 1}: ${test.name}`);
                console.log('输入:', test.code);
                
                const formatted = formatJavaScript(test.code);
                console.log('输出:', formatted);
                
                const issues = checkOperatorIssues(formatted);
                const passed = issues.length === 0;
                
                if (passed) passCount++;

                const div = document.createElement('div');
                div.className = `test ${passed ? 'pass' : 'fail'}`;
                div.innerHTML = `
                    <strong>${passed ? '✅' : '❌'} ${test.name}</strong><br>
                    输入: <code>${test.code}</code><br>
                    输出: <code>${formatted}</code><br>
                    ${test.expected ? `期望: <code>${test.expected}</code><br>` : ''}
                    结果: ${passed ? '✅ 通过' : '❌ ' + issues.join(', ')}
                `;
                results.appendChild(div);
            });

            // 总结
            const summary = document.createElement('div');
            summary.className = `test ${passCount === operatorTests.length ? 'pass' : 'fail'}`;
            summary.innerHTML = `
                <h2>${passCount === operatorTests.length ? '🎉' : '⚠️'} 测试总结</h2>
                <div>通过: ${passCount}/${operatorTests.length}</div>
                <div>状态: ${passCount === operatorTests.length ? '✅ 所有操作符正常' : '❌ 仍有问题'}</div>
            `;
            results.insertBefore(summary, results.firstChild);

            console.log(`\n总结: ${passCount}/${operatorTests.length} 通过`);
            return passCount === operatorTests.length;
        }

        // 自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('开始操作符专项测试...');
            runOperatorTests();
        });
    </script>
</body>
</html>
