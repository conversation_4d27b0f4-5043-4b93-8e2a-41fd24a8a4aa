<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注释和操作符格式化测试</title>
    <style>
        body {
            font-family: monospace;
            margin: 20px;
            background: #000;
            color: #0f0;
        }
        .test {
            border: 2px solid;
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .pass { border-color: #0f0; background: rgba(0, 255, 0, 0.1); }
        .fail { border-color: #f00; background: rgba(255, 0, 0, 0.1); color: #f00; }
        .code {
            background: #222;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            border-left: 4px solid #555;
            font-size: 14px;
        }
        .before { border-left-color: #ff0; }
        .after { border-left-color: #0ff; }
        .error { color: #f00; font-weight: bold; }
        .success { color: #0f0; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🔧 注释和操作符格式化测试</h1>
    <p>专门测试用户反馈的注释和操作符问题</p>

    <div id="results"></div>

    <script>
        // 修复后的格式化函数
        function formatJavaScript(code) {
            if (!code || code.trim() === '') return code;

            try {
                console.log('开始格式化:', code);

                // 1. 保护字符串内容
                const strings = [];
                let stringIndex = 0;
                let formatted = code.replace(/(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g, (match) => {
                    const placeholder = `__STR_${stringIndex++}__`;
                    strings.push({ placeholder, content: match });
                    return placeholder;
                });
                console.log('保护字符串后:', formatted);

                // 2. 保护注释内容
                const comments = [];
                let commentIndex = 0;
                // 保护单行注释
                formatted = formatted.replace(/\/\/.*$/gm, (match) => {
                    const placeholder = `__COMMENT_${commentIndex++}__`;
                    comments.push({ placeholder, content: match });
                    console.log('发现单行注释:', match, '->', placeholder);
                    return placeholder;
                });
                // 保护多行注释
                formatted = formatted.replace(/\/\*[\s\S]*?\*\//g, (match) => {
                    const placeholder = `__COMMENT_${commentIndex++}__`;
                    comments.push({ placeholder, content: match });
                    console.log('发现多行注释:', match, '->', placeholder);
                    return placeholder;
                });
                console.log('保护注释后:', formatted);

                // 3. 保护箭头函数
                const arrows = [];
                let arrowIndex = 0;
                formatted = formatted.replace(/\s*=>\s*/g, () => {
                    const placeholder = `__ARROW_${arrowIndex++}__`;
                    arrows.push({ placeholder, content: ' => ' });
                    console.log('发现箭头函数，替换为:', placeholder);
                    return placeholder;
                });
                console.log('保护箭头函数后:', formatted);

                // 4. 基本空白处理
                formatted = formatted.replace(/\s+/g, ' ').trim();
                console.log('空白处理后:', formatted);

                // 5. 处理操作符（使用负向前瞻避免冲突）
                formatted = formatted
                    // 先处理长的操作符，避免被短的匹配
                    .replace(/\s*(===)\s*/g, ' $1 ')  // 严格相等
                    .replace(/\s*(!==)\s*/g, ' $1 ')  // 严格不等
                    .replace(/\s*(<=)\s*/g, ' $1 ')   // 小于等于
                    .replace(/\s*(>=)\s*/g, ' $1 ')   // 大于等于
                    .replace(/\s*(==)\s*/g, ' $1 ')   // 相等
                    .replace(/\s*(!=)\s*/g, ' $1 ')   // 不等
                    .replace(/\s*(<)(?!=)\s*/g, ' $1 ')    // 小于（不是<=）
                    .replace(/\s*(>)(?!=)\s*/g, ' $1 ')    // 大于（不是>=）
                    .replace(/\s*(&&)\s*/g, ' $1 ')   // 逻辑与
                    .replace(/\s*(\|\|)\s*/g, ' $1 ') // 逻辑或
                    // 算术操作符（避免影响注释）
                    .replace(/\s*([+\-])\s*/g, ' $1 ')     // 加减
                    .replace(/\s*([*%])\s*/g, ' $1 ')      // 乘模（不包括除法，避免影响注释）
                    // 赋值操作符
                    .replace(/\s*([a-zA-Z_$][a-zA-Z0-9_$]*|\]|\))\s*=\s*([^=])/g, '$1 = $2');
                console.log('操作符处理后:', formatted);

                // 6. 处理标点
                formatted = formatted
                    .replace(/,(?!\s)/g, ', ')
                    .replace(/;(?!\s*\n)/g, ';\n')
                    .replace(/\{(?!\s*\n)/g, ' {\n')
                    .replace(/\}(?!\s*\n)/g, '\n}')
                    .replace(/\s*\(\s*/g, '(')
                    .replace(/\s*\)\s*/g, ')');
                console.log('标点处理后:', formatted);

                // 7. 最终清理
                formatted = formatted
                    .replace(/\s*{\s*/g, ' {\n')
                    .replace(/\s*}\s*/g, '\n}')
                    .replace(/\s*;\s*/g, ';\n')
                    .replace(/\s*,\s*/g, ', ');
                console.log('最终清理后:', formatted);

                // 8. 恢复箭头函数
                arrows.forEach(({ placeholder, content }) => {
                    console.log('恢复箭头函数:', placeholder, '->', content);
                    formatted = formatted.replace(placeholder, content);
                });
                console.log('恢复箭头函数后:', formatted);

                // 9. 恢复注释
                comments.forEach(({ placeholder, content }) => {
                    console.log('恢复注释:', placeholder, '->', content);
                    formatted = formatted.replace(placeholder, content);
                });
                console.log('恢复注释后:', formatted);

                // 10. 恢复字符串
                strings.forEach(({ placeholder, content }) => {
                    formatted = formatted.replace(placeholder, content);
                });
                console.log('恢复字符串后:', formatted);

                // 11. 缩进
                const lines = formatted.split('\n');
                let indent = 0;
                const result = lines.map(line => {
                    line = line.trim();
                    if (!line) return '';
                    if (line.includes('}')) indent = Math.max(0, indent - 1);
                    const indented = '    '.repeat(indent) + line;
                    if (line.includes('{')) indent++;
                    return indented;
                }).join('\n');

                console.log('最终结果:', result);
                return result;

            } catch (error) {
                console.error('格式化错误:', error);
                return code;
            }
        }

        // 验证函数
        function validate(code) {
            try {
                new Function(code);
                return true;
            } catch {
                return false;
            }
        }

        // 检查特定问题
        function checkIssues(code) {
            const issues = [];
            
            // 检查注释问题
            if (code.includes('/ /')) {
                issues.push('注释符号被分割: "/ /"');
            }
            
            // 检查操作符问题
            if (code.includes('!= =')) {
                issues.push('不等于符号被分割: "!= ="');
            }
            if (code.includes('= = =')) {
                issues.push('严格相等符号被分割: "= = ="');
            }
            if (code.includes('< =')) {
                issues.push('小于等于符号被分割: "< ="');
            }
            if (code.includes('> =')) {
                issues.push('大于等于符号被分割: "> ="');
            }
            if (code.includes('& &')) {
                issues.push('逻辑与符号被分割: "& &"');
            }
            if (code.includes('| |')) {
                issues.push('逻辑或符号被分割: "| |"');
            }
            
            return issues;
        }

        // 测试用例
        const tests = [
            {
                name: '用户反馈的原始问题',
                code: `(data,params,refs)=>{
function sortByValueAndKey(arr) {
return [...arr].sort((a, b) => {
// 先按 value 排序
const valueCompare = a.TEMPLATE_CODE.localeCompare(b.TEMPLATE_CODE);
if (valueCompare !== 0) return valueCompare;
// 如果 value 相同，则按 key 排序
return a.EQUIPMENT_NAME - b.EQUIPMENT_NAME;
});
}
return sortByValueAndKey(data)
}`
            },
            {
                name: '注释测试',
                code: '// 这是单行注释\nlet a = 1; // 行尾注释\n/* 多行注释 */'
            },
            {
                name: '操作符测试',
                code: 'if(a!==b&&c<=d||e>=f){return true}'
            },
            {
                name: '混合测试',
                code: '(x)=>{// 注释\nreturn x!==null&&x>=0}'
            }
        ];

        // 运行测试
        function runTests() {
            const results = document.getElementById('results');
            let passCount = 0;

            tests.forEach((test, index) => {
                console.log(`\n=== 测试 ${index + 1}: ${test.name} ===`);
                
                const original = test.code;
                const formatted = formatJavaScript(original);
                
                const originalValid = validate(original);
                const formattedValid = validate(formatted);
                const issues = checkIssues(formatted);
                
                const passed = originalValid && formattedValid && issues.length === 0;
                if (passed) passCount++;

                const div = document.createElement('div');
                div.className = `test ${passed ? 'pass' : 'fail'}`;
                div.innerHTML = `
                    <h3>${passed ? '✅' : '❌'} 测试 ${index + 1}: ${test.name}</h3>
                    <div><strong>原始代码:</strong></div>
                    <div class="code before">${original}</div>
                    <div><strong>格式化后:</strong></div>
                    <div class="code after">${formatted}</div>
                    <div><strong>检查结果:</strong></div>
                    <div>
                        原始语法: <span class="${originalValid ? 'success' : 'error'}">${originalValid ? '✅' : '❌'}</span><br>
                        格式化语法: <span class="${formattedValid ? 'success' : 'error'}">${formattedValid ? '✅' : '❌'}</span><br>
                        问题检查: <span class="${issues.length === 0 ? 'success' : 'error'}">
                            ${issues.length === 0 ? '✅ 无问题' : '❌ ' + issues.join(', ')}
                        </span><br>
                        <strong>总体: <span class="${passed ? 'success' : 'error'}">${passed ? '✅ 通过' : '❌ 失败'}</span></strong>
                    </div>
                `;
                results.appendChild(div);
            });

            // 总结
            const summary = document.createElement('div');
            summary.className = `test ${passCount === tests.length ? 'pass' : 'fail'}`;
            summary.innerHTML = `
                <h2>${passCount === tests.length ? '🎉' : '⚠️'} 测试总结</h2>
                <div>通过: ${passCount}/${tests.length}</div>
                <div>状态: ${passCount === tests.length ? '✅ 所有问题已解决' : '❌ 仍有问题需要修复'}</div>
            `;
            results.insertBefore(summary, results.firstChild);

            return passCount === tests.length;
        }

        // 自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('开始注释和操作符格式化测试...');
            const result = runTests();
            
            if (result) {
                console.log('\n🎉 恭喜！注释和操作符格式化问题已解决！');
            } else {
                console.log('\n⚠️ 仍有问题需要修复。');
            }
        });
    </script>
</body>
</html>
