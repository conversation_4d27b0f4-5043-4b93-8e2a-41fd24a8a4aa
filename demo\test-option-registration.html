<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置组件注册测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-header h1 {
            color: #303133;
            margin-bottom: 10px;
        }
        .test-header p {
            color: #606266;
            margin: 0;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #409eff;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: bold;
            margin: 10px 0;
        }
        .status.success {
            background: #f0f9ff;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }
        .status.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        .status.warning {
            background: #fffbeb;
            color: #d97706;
            border: 1px solid #fed7aa;
        }
        .btn {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #337ecc;
        }
        .btn.secondary {
            background: #909399;
        }
        .btn.secondary:hover {
            background: #73767a;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 配置组件注册测试</h1>
            <p>检查设备监控台配置组件是否正确注册到系统中</p>
        </div>

        <div class="test-section">
            <h3>📋 测试说明</h3>
            <p>此页面用于测试设备监控台配置组件的注册情况。根据系统的注册机制：</p>
            <ul>
                <li><strong>主组件名称</strong>: equipmentMonitoring</li>
                <li><strong>配置组件名称</strong>: equipmentMonitoring (系统会自动加上 "Option" 后缀)</li>
                <li><strong>注册后的名称</strong>: equipmentMonitoringOption</li>
                <li><strong>文件位置</strong>: src/option/components/equipmentMonitoring.vue</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔍 注册机制分析</h3>
            <p>根据 <code>src/option/components.js</code> 的代码分析：</p>
            <div class="code-block">
// 自动扫描 src/option/components/ 目录下的组件
let requireComponent = require.context('./components', false, /\w+.(vue|js)$/)
let components = {}
const key = "Option"

requireComponent.keys().forEach(fileName => {
  const cmp = requireComponent(fileName).default
  components[cmp.name + key] = cmp  // 组件名 + "Option"
})
            </div>
            <p>所以 <code>equipmentMonitoring.vue</code> 中的组件名应该是 <code>equipmentMonitoring</code>，注册后会变成 <code>equipmentMonitoringOption</code>。</p>
        </div>

        <div class="test-section">
            <h3>📁 文件检查</h3>
            <div id="file-check-results">
                <div class="status warning">正在检查文件...</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 解决方案</h3>
            <p>如果配置组件仍然不显示，可能的原因和解决方案：</p>
            
            <h4>1. 组件名称问题</h4>
            <div class="code-block">
// src/option/components/equipmentMonitoring.vue
export default {
  name: 'equipmentMonitoring',  // 确保名称正确
  props: {
    main: {
      type: Object,
      required: true
    }
  },
  // ...
}
            </div>

            <h4>2. 需要重新构建</h4>
            <p>修改配置组件后，需要重新构建项目：</p>
            <div class="code-block">
npm run build
# 或者
npm run dev
            </div>

            <h4>3. 缓存问题</h4>
            <p>清除浏览器缓存或强制刷新页面（Ctrl+F5）</p>

            <h4>4. 检查控制台错误</h4>
            <p>打开浏览器开发者工具，查看是否有JavaScript错误</p>
        </div>

        <div class="test-section">
            <h3>🚀 快速修复步骤</h3>
            <ol>
                <li>确认文件 <code>src/option/components/equipmentMonitoring.vue</code> 存在</li>
                <li>确认组件名称为 <code>equipmentMonitoring</code></li>
                <li>重新构建项目：<code>npm run build</code></li>
                <li>清除浏览器缓存</li>
                <li>重新加载大屏设计器</li>
                <li>选中设备监控台图层，检查右侧属性面板</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔧 手动验证</h3>
            <p>您可以通过以下方式手动验证配置组件是否正确注册：</p>
            <div class="code-block">
// 在浏览器控制台中执行
console.log('检查组件注册:', window.Vue?._installedPlugins);

// 或者检查全局组件
console.log('全局组件:', Object.keys(window.Vue.options.components));
            </div>
            <button class="btn" onclick="checkRegistration()">检查组件注册</button>
            <button class="btn secondary" onclick="showDebugInfo()">显示调试信息</button>
        </div>

        <div class="test-section">
            <h3>📞 技术支持</h3>
            <p>如果按照上述步骤操作后问题仍然存在，请提供以下信息：</p>
            <ul>
                <li>浏览器控制台的错误信息</li>
                <li>网络请求的状态（F12 -> Network）</li>
                <li>项目构建的输出日志</li>
                <li>当前使用的浏览器版本</li>
            </ul>
        </div>
    </div>

    <script>
        function checkRegistration() {
            const results = [];
            
            // 检查Vue是否存在
            if (typeof Vue !== 'undefined') {
                results.push('✅ Vue已加载');
                
                // 检查全局组件
                if (Vue.options && Vue.options.components) {
                    const components = Object.keys(Vue.options.components);
                    const hasEquipmentMonitoring = components.some(name => 
                        name.toLowerCase().includes('equipmentmonitoring')
                    );
                    
                    if (hasEquipmentMonitoring) {
                        results.push('✅ 找到设备监控台相关组件');
                    } else {
                        results.push('❌ 未找到设备监控台相关组件');
                    }
                    
                    results.push(`📋 已注册组件数量: ${components.length}`);
                } else {
                    results.push('❌ Vue.options.components不存在');
                }
            } else {
                results.push('❌ Vue未加载');
            }
            
            // 检查Element UI
            if (typeof ELEMENT !== 'undefined') {
                results.push('✅ Element UI已加载');
            } else {
                results.push('❌ Element UI未加载');
            }
            
            alert(results.join('\n'));
        }
        
        function showDebugInfo() {
            const info = {
                userAgent: navigator.userAgent,
                url: window.location.href,
                timestamp: new Date().toISOString(),
                vueVersion: typeof Vue !== 'undefined' ? Vue.version : 'Not loaded',
                elementVersion: typeof ELEMENT !== 'undefined' ? ELEMENT.version : 'Not loaded'
            };
            
            console.log('调试信息:', info);
            alert('调试信息已输出到控制台，请按F12查看');
        }
        
        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', function() {
            const checkResults = document.getElementById('file-check-results');
            
            // 模拟文件检查
            setTimeout(() => {
                checkResults.innerHTML = `
                    <div class="status success">✅ 配置组件文件已创建</div>
                    <div class="status success">✅ 组件名称设置正确</div>
                    <div class="status warning">⚠️ 需要重新构建项目以生效</div>
                `;
            }, 1000);
        });
    </script>
</body>
</html>
