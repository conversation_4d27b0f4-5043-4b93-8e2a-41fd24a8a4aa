/**
 * 格式化代码优化测试脚本
 * 用于验证重复点击格式化代码的问题是否已解决
 */

// 模拟Vue组件的格式化方法（优化前）
class FormatCodeBefore {
    constructor() {
        this.code = '';
        this.formatCount = 0;
    }

    async formatCode() {
        this.formatCount++;
        console.log(`[优化前] 开始格式化 #${this.formatCount}`);
        
        // 没有防重复点击保护
        try {
            // 模拟格式化过程
            await this.delay(500 + Math.random() * 500);
            console.log(`[优化前] 格式化完成 #${this.formatCount}`);
            return `格式化完成 #${this.formatCount}`;
        } catch (error) {
            console.error(`[优化前] 格式化失败 #${this.formatCount}:`, error);
            throw error;
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 模拟Vue组件的格式化方法（优化后）
class FormatCodeAfter {
    constructor() {
        this.code = '';
        this.formatCount = 0;
        this.isFormatting = false;
        this.formatClickCount = 0;
    }

    async formatCode() {
        this.formatCount++;
        
        // 防重复点击保护
        if (this.isFormatting) {
            this.formatClickCount++;
            console.log(`[优化后] 格式化请求 #${this.formatCount} 被忽略（正在格式化中，队列: ${this.formatClickCount}）`);
            return Promise.reject(new Error(`格式化正在进行中，请稍候（忽略第${this.formatClickCount}次点击）`));
        }

        this.isFormatting = true;
        this.formatClickCount = 1;
        console.log(`[优化后] 开始格式化 #${this.formatCount}`);
        
        try {
            // 模拟格式化过程
            await this.delay(500 + Math.random() * 500);
            console.log(`[优化后] 格式化完成 #${this.formatCount}`);
            
            if (this.formatClickCount > 1) {
                console.log(`[优化后] 格式化完成，期间忽略了${this.formatClickCount - 1}次重复点击`);
            }
            
            return `格式化完成 #${this.formatCount}`;
        } catch (error) {
            console.error(`[优化后] 格式化失败 #${this.formatCount}:`, error);
            throw error;
        } finally {
            this.isFormatting = false;
            this.formatClickCount = 0;
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 测试函数
async function testFormatCode() {
    console.log('🔧 开始测试格式化代码优化...\n');

    // 测试优化前的版本
    console.log('=== 测试1: 优化前的版本（有问题） ===');
    const beforeInstance = new FormatCodeBefore();
    
    // 快速连续调用10次
    const beforePromises = [];
    for (let i = 0; i < 10; i++) {
        beforePromises.push(beforeInstance.formatCode());
        await new Promise(resolve => setTimeout(resolve, 50)); // 50ms间隔
    }
    
    try {
        const beforeResults = await Promise.allSettled(beforePromises);
        console.log(`[优化前] 完成了 ${beforeResults.filter(r => r.status === 'fulfilled').length} 个格式化操作`);
        console.log(`[优化前] 失败了 ${beforeResults.filter(r => r.status === 'rejected').length} 个格式化操作\n`);
    } catch (error) {
        console.error('[优化前] 测试出错:', error);
    }

    // 等待一段时间
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 测试优化后的版本
    console.log('=== 测试2: 优化后的版本（已修复） ===');
    const afterInstance = new FormatCodeAfter();
    
    // 快速连续调用10次
    const afterPromises = [];
    for (let i = 0; i < 10; i++) {
        afterPromises.push(afterInstance.formatCode().catch(err => err));
        await new Promise(resolve => setTimeout(resolve, 50)); // 50ms间隔
    }
    
    try {
        const afterResults = await Promise.allSettled(afterPromises);
        const successful = afterResults.filter(r => r.status === 'fulfilled' && typeof r.value === 'string').length;
        const ignored = afterResults.filter(r => r.status === 'fulfilled' && r.value instanceof Error).length;
        const failed = afterResults.filter(r => r.status === 'rejected').length;
        
        console.log(`[优化后] 成功完成了 ${successful} 个格式化操作`);
        console.log(`[优化后] 忽略了 ${ignored} 个重复请求`);
        console.log(`[优化后] 失败了 ${failed} 个格式化操作\n`);
    } catch (error) {
        console.error('[优化后] 测试出错:', error);
    }

    console.log('🎉 测试完成！');
    console.log('\n总结:');
    console.log('- 优化前: 可能出现多个格式化操作同时进行，导致状态混乱');
    console.log('- 优化后: 同时只能有一个格式化操作，重复点击会被优雅地忽略');
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { FormatCodeBefore, FormatCodeAfter, testFormatCode };
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    window.FormatCodeBefore = FormatCodeBefore;
    window.FormatCodeAfter = FormatCodeAfter;
    window.testFormatCode = testFormatCode;
    
    // 自动运行测试
    document.addEventListener('DOMContentLoaded', function() {
        console.log('页面加载完成，开始运行测试...');
        testFormatCode();
    });
}

// 导出测试结果验证函数
function validateOptimization() {
    const issues = [];
    
    // 检查是否添加了防重复点击保护
    const codeContent = `
        // 检查点1: 是否有状态标志
        if (!this.isFormatting) {
            issues.push('缺少isFormatting状态标志');
        }
        
        // 检查点2: 是否有重复点击计数
        if (!this.formatClickCount) {
            issues.push('缺少formatClickCount计数器');
        }
        
        // 检查点3: 是否在finally中重置状态
        // this.isFormatting = false;
        
        // 检查点4: 是否有用户友好的提示
        // this.$message.warning('格式化正在进行中，请稍候');
    `;
    
    return {
        isOptimized: issues.length === 0,
        issues: issues,
        recommendations: [
            '添加isFormatting状态标志防止重复调用',
            '添加formatClickCount计数器跟踪重复点击',
            '在finally块中重置状态确保清理',
            '提供用户友好的提示信息',
            '禁用按钮状态防止用户重复点击',
            '显示加载状态提升用户体验'
        ]
    };
}

console.log('格式化代码优化测试脚本已加载');
